import { useEffect } from "react";
import { useQuery, keepPreviousData } from "@tanstack/react-query";
import usePaginationRN from "../use-pagination-rn";
import { ProductType } from "../../types/products";
import { PaginationType } from "../../types/pagination";
import { retrieveSimilarProductsFromServerSide } from "../../services/products/similar-products-extraction-rn";
import { CriteriaType } from "../../types";
import { retrieveProductsFromServerSide } from "../../services/products/products-extraction-rn";
import { catalogQueryKeys } from "../../lib/query-client";

interface Params {
  limit: number;
  criteria?: CriteriaType;
  similarProductSlug?: string;
  categoriesSlugs?: string[];
  brandSlugs?: string[];
  queryKeys?: (string | number)[];
  search?: string;
}

export default function useProductsRN({
  limit,
  criteria,
  similarProductSlug,
  categoriesSlugs,
  brandSlugs,
  search,
  queryKeys = [],
}: Params) {
  const { page, setPage, pagesNumber, setPagesNumber, paginatedListRef } =
    usePaginationRN();

  const { data, isLoading, error, refetch } = useQuery<{
    products: ProductType[];
    pagination: PaginationType;
  } | null>({
    queryKey: [
      ...catalogQueryKeys.products({
        page,
        criteria,
        similarProductSlug,
        categoriesSlugs,
        brandSlugs,
        search,
      }),
      ...queryKeys,
    ],
    queryFn: () =>
      similarProductSlug
        ? retrieveSimilarProductsFromServerSide({
            page,
            limit,
            productSlug: similarProductSlug,
          })
        : retrieveProductsFromServerSide({
            page,
            limit,
            categoriesSlugs,
            brandSlugs,
            criteria,
            search,
          }),
    placeholderData: keepPreviousData,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  useEffect(() => {
    setPagesNumber(
      data?.pagination?.totalPages ? data?.pagination?.totalPages : 1
    );
  }, [data, setPagesNumber]);

  return {
    products: data?.products,
    productsAreLoading: isLoading,
    productsError: error,
    setPage,
    page,
    pagesNumber,
    paginatedListRef,
    refetch,
  };
}

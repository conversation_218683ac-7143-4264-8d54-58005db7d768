import { useEffect, useState } from "react";
import { retrieveMinMaxPriceFromServerSide } from "../services/products/min-max-price-extraction";

export default function useRangeSlider() {
  const [value, setValue] = useState([0, 1000]);
  const [minMaxPrice, setMinMaxPrice] = useState([0, 1000]);
  const [isLoading, setIsLoading] = useState(true);

  //extracting min max price from server side
  useEffect(() => {
    retrieveMinMaxPriceFromServerSide().then((res) => {
      if (res) {
        setMinMaxPrice([res.minPrice, res.maxPrice]);
        setValue([res.minPrice, res.maxPrice]);
        setIsLoading(false);
      }
    });
  }, []);

  function handleRangeSliderChangement(updateValue: number[]) {
    if (updateValue[0] < updateValue[1]) setValue(updateValue);
  }

  return {
    value,
    handleRangeSliderChangement,
    isLoading,
    minMaxPrice,
  };
}

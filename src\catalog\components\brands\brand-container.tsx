import { Button } from "@/components/ui/button";
import { BrandType } from "../../types/brands";
import { getBrandPageUrl } from "../../utils/urls";
import Link from "next/link";
import Image from "next/image";

interface Props {
  brand: BrandType;
}

export default function BrandContainer({ brand }: Props) {
  return (
    <Button
      variant="ghost"
      className="w-full L:h-[115px] h-[95px] overflow-hidden group border border-gray-light bg-white rounded-lg flex items-center justify-center hover:border-primary transition-colors"
    >
      <Link
        href={getBrandPageUrl(brand.slug)}
        className="w-full h-full flex items-center justify-center"
      >
        <Image
          src={brand.image}
          alt={brand.name}
          width={120}
          height={100}
          unoptimized
          className="L:h-[100px] h-20 w-full object-contain group-hover:scale-110 duration-200"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = "/not-found/product-image.webp";
          }}
        />
      </Link>
    </Button>
  );
}

import { CriteriaType } from "../types";
import { BrandType } from "../types/brands";
import { CategorySelectionType, CategoryType } from "../types/categories";
import { create } from "zustand";
import { getSelectedCategoryWithItsSubCategories } from "../utils/categories-extraction";

interface FilterState {
  joinedPageData: {
    category?: CategoryType | null;
    brand?: BrandType | null;
  };
  joinedPageParam: {
    brandSlug?: string | null;
    categorySlug?: string | null;
    subCategorySlug?: string | null;
    subSubCategorySlug?: string | null;
  };
  priceRangeFilteringVersion: number;
  selectedBrands: BrandType[];
  categories: CategorySelectionType[];
  priceRange: number[];
  criteria: CriteriaType;
  search: string;
  filterVersion: number;
}

interface FilterActions {
  setJoinedPageParam: (params: FilterState["joinedPageParam"]) => void;
  setJoinedPageData: (data: FilterState["joinedPageData"]) => void;
  setSearch: (search: string) => void;
  setCategories: (categories: CategorySelectionType[]) => void;
  initializeCategories: (
    categories: CategoryType[],
    categoriesSlugsInUrl: string[]
  ) => void;
  addBrand: (brand: BrandType) => void;
  removeBrand: (brandId: string) => void;
  setBrands: (brands: BrandType[]) => void;
  setPriceRange: (priceRange: number[]) => void;
  applyPriceRangeFiltering: () => void;
  setCriteria: (criteria: CriteriaType) => void;
  clearAll: () => void;
  resetFilter: () => void;
  clearAllCategories: () => void;
  applyFilter: () => void;
}

const initialState: FilterState = {
  categories: [],
  selectedBrands: [],
  priceRange: [],
  criteria: "displayOrder",
  search: "",
  priceRangeFilteringVersion: 0,
  joinedPageParam: {},
  joinedPageData: {},
  filterVersion: 0,
};

export const useProductsFilteringStore = create<FilterState & FilterActions>(
  (set) => ({
    // Initial State
    ...initialState,
    // Actions
    setJoinedPageParam: (params) => set({ joinedPageParam: params }),
    setJoinedPageData: (data) => set({ joinedPageData: data }),
    setSearch: (search) => set({ search }),
    setCategories: (categories) => set({ categories }),
    initializeCategories: (categories, categoriesSlugsInUrl) =>
      set((state) => ({
        categories: categories.map((cat) =>
          castToCategorySelectionType(cat, categoriesSlugsInUrl)
        ),
      })),
    addBrand: (brand) =>
      set((state) => ({
        selectedBrands: [...state.selectedBrands, brand],
      })),
    removeBrand: (brandId) =>
      set((state) => ({
        selectedBrands: state.selectedBrands.filter(
          (bra) => bra.id !== brandId
        ),
      })),
    setBrands: (brands) =>
      set((state) => ({
        selectedBrands: brands,
      })),
    setPriceRange: (priceRange) => set({ priceRange }),
    applyPriceRangeFiltering: () =>
      set((state) => ({
        priceRangeFilteringVersion: state.priceRangeFilteringVersion + 1,
      })),
    setCriteria: (criteria) => set({ criteria }),
    clearAllCategories: () =>
      set((state) => ({
        categories: deselectCategories(state.categories),
      })),
    clearAll: () =>
      set((state) => ({
        search: "",
        selectedBrands: state.joinedPageData.brand
          ? [state.joinedPageData.brand]
          : [],
        categories: deselectCategories(state.categories),
        filterVersion: state.filterVersion + 1,
      })),
    resetFilter: () =>
      set((state) => ({
        search: "",
        selectedBrands: [],
        categories: [],
      })),
    applyFilter: () =>
      set((state) => ({
        filterVersion: state.filterVersion + 1,
      })),
  })
);

const castToCategorySelectionType = (
  category: CategoryType,
  categoriesSlugsInUrl?: string[]
): CategorySelectionType => {
  const categorySubCategories = category.subCategories.map((subCategory) =>
    castToCategorySelectionType(subCategory, categoriesSlugsInUrl)
  );

  return {
    ...category,
    subCategories: categorySubCategories,
    selected:
      categoriesSlugsInUrl !== undefined &&
      categoriesSlugsInUrl.includes(category.slug),
  };
};

const deselectCategories = (categories: CategorySelectionType[]) => {
  return categories.map((category) => {
    if (category.selected) {
      category.selected = false;
    }
    if (category.subCategories.length > 0) {
      category.subCategories = deselectCategories(category.subCategories);
    }
    return category;
  });
};

export const getSelectedCategories = () => {
  const categories = useProductsFilteringStore.getState().categories;
  const joinedPageCategory =
    useProductsFilteringStore.getState().joinedPageData.category;

  //if there's no categories so we're in a category page and this category doesn't have subcategories
  if (categories.length === 0) {
    return joinedPageCategory ? [joinedPageCategory] : [];
  }
  let selectedCategories: CategorySelectionType[] = [];

  categories.forEach((category) => {
    if (category.selected) {
      let categoryHasSelectedSubCategories = false;

      category.subCategories.forEach((subCategory) => {
        if (subCategory.selected) {
          categoryHasSelectedSubCategories = true;

          selectedCategories = [
            ...selectedCategories, //add the selected category with its subcategories
            ...getSelectedCategoryWithItsSubCategories(subCategory),
          ];
        }
      });

      //we need to push the category also bc we need to extract its products if it has products
      //it can have products that we don't have it in its subcategories
      if (!categoryHasSelectedSubCategories)
        selectedCategories = [
          ...selectedCategories,
          ...getSelectedCategoryWithItsSubCategories(category),
        ];
      //pushing selected category with its subcategories
      else selectedCategories.push(category); //pushing category with its subcategories already added
    }
  });

  //by default all categories are not selected so we need to filter based on the all categories
  if (selectedCategories.length === 0) {
    const allCategories = categories.flatMap((cat) =>
      getSelectedCategoryWithItsSubCategories(cat)
    );
    //those categories aare subcategories of the joined page category so we need to return the category joined to make sure that we've its subcategories
    return joinedPageCategory
      ? [joinedPageCategory, ...allCategories]
      : allCategories;
  } else return selectedCategories;
};

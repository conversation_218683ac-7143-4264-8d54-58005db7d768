import Text from "@/styles/text-styles";
import { ReactElement, useEffect, useState } from "react";
import {
  getSelectedCategories,
  useProductsFilteringStore,
} from "@/modules/catalog/store/products-filter";
import { useTranslations } from "next-intl";
import FilterIcon from "@assets/icons/filter";
import { Button } from "@/components/ui/button";
import useBrands from "../../../hooks/brands/use-brands";
import FilterDrawer from "../filter-drawer";
import SortingOptions from "../sorting-options";
import { notFound, usePathname, useSearchParams } from "next/navigation";
import useCategories from "../../../hooks/categories/use-categories";
import useMinMaxPrice from "../../../hooks/products/use-min-max-price";
import BreadCrumb from "@/components/breadcrumb";

interface Props {
  children: ReactElement;
  productCount?: number; // Add product count prop
}

export default function FilterPageProductsWrapper({
  children,
  productCount,
}: Props) {
  const t = useTranslations("filtersPage");
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const searchedProducts = searchParams.get("search");
  const brandsIdsInParams = searchParams.get("brandsSlugs");
  const defaultSelectedBrandsFromParams = brandsIdsInParams
    ? brandsIdsInParams.split(",")
    : [];
  const categoriesSlugsFromParams = searchParams.get("categoriesSlugs");
  const defaultSelectedCategoriesFromParams = categoriesSlugsFromParams
    ? categoriesSlugsFromParams.split(",")
    : [];

  const {
    categories: extractedCategories,
    categoriesAreLoading: extractedCategoriesAreLoading,
  } = useCategories();

  const { prices: minMaxPrices } = useMinMaxPrice();

  const {
    joinedPageParam,
    setJoinedPageData,
    search,
    setSearch,
    setPriceRange,
    setBrands,
    initializeCategories,
    addBrand,
    resetFilter,
    priceRange,
  } = useProductsFilteringStore();
  const { brandSlug } = joinedPageParam;

  const { brands, brandsAreLoading } = useBrands({
    limit: 500,
    categoriesSlugs: brandSlug
      ? undefined
      : getSelectedCategories().map((cat) => cat.slug),
    productPriceRange: brandSlug ? undefined : priceRange,
    searchByProductName: brandSlug ? undefined : search,
  });

  const [pageTitle, setPageTitle] = useState(t("title"));
  const [filterIsOpen, setFilterIsOpen] = useState(false);

  /* initial price slider set up */
  useEffect(() => {
    const priceRangeInParam = searchParams.get("priceRange");

    const initialPricesSetup = () => {
      if (priceRangeInParam) {
        const prices = priceRangeInParam
          .split(",")
          .map((price) => Number(price));

        if (prices.length === 2) setPriceRange([prices[0], prices[1]]); // set the price range state

        return;
      }

      setPriceRange([minMaxPrices[0], minMaxPrices[1]]);
    };

    initialPricesSetup();
  }, [...minMaxPrices]);

  //filtering all results based on the passed search param
  useEffect(() => {
    if (searchedProducts) setSearch(searchedProducts);

    return () => {
      resetFilter(); //reset filter when leaving the page
      setSearch("");
    };
  }, [pathname, searchedProducts]);
  useEffect(() => {
    if (!brandsAreLoading && brands) {
      if (brandSlug) {
        const brand = brands.find(
          (searchedBrand) => searchedBrand.slug === brandSlug
        );

        if (brand) {
          addBrand(brand);
          setJoinedPageData({ brand });
          setPageTitle(brand.name);
        } else notFound();
      } else if (defaultSelectedBrandsFromParams.length > 0) {
        const brandsFromParams = brands.filter((brand) =>
          defaultSelectedBrandsFromParams.includes(brand.slug)
        );

        if (brandsFromParams.length > 0) {
          setBrands(brandsFromParams);
        }
      }
    }
  }, [brands, brandsAreLoading, brandSlug, pathname]);

  useEffect(() => {
    if (
      !extractedCategoriesAreLoading &&
      extractedCategories &&
      extractedCategories.length > 0
    ) {
      initializeCategories(
        extractedCategories,
        defaultSelectedCategoriesFromParams
      );
    }
  }, [extractedCategories, extractedCategoriesAreLoading, pathname]);

  return (
    <div className="w-full flex flex-col gap-3">
      {/* Breadcrumbs */}
      <BreadCrumb />

      {/* Category Name with Product Count */}
      <div className="flex flex-col regularL:flex-row text-left mb-6">
        {search !== "" ? (
          <h1 className="w-full flex justify-start text-gray-dark font-bold">
            {search && (
              <Text textStyle="TS2" className="font-bold">
                <span>
                  {t.raw("search")} : &quot; {search} &quot;
                </span>{" "}
              </Text>
            )}
            {productCount && (
              <span className="text-gray ml-2">({productCount})</span>
            )}
          </h1>
        ) : (
          <h1 className="w-full flex justify-start text-gray-dark font-bold">
            <Text textStyle="TS2">{pageTitle}</Text>
            {productCount && (
              <span className="text-gray ml-2">({productCount})</span>
            )}
          </h1>
        )}
        {/* Selected buttons */}
        <SortingOptions />
      </div>

      {/* Sidebar */}
      <div className="pt-3 w-full flex justify-between">
        <Button
          variant="link"
          className="2extraL:hidden L:px-0 L:hover:before:w-full hover:before:w-0 L:hover:bg-transparent L:hover:text-primary before:bg-primary hover:bg-primary hover:text-white L:bg-transparent bg-primary L:text-primary text-white L:relative L:bottom-0 L:right-0 L:z-0 fixed bottom-20 left-5 w-fit L:shadow-none shadow-md z-50"
          onClick={() => setFilterIsOpen(true)}
        >
          <span className="L:stroke-primary stroke-white">
            <FilterIcon />
          </span>
          <Text textStyle="TS5" className="font-bold">
            {t("filterHeader")}
          </Text>
        </Button>
      </div>

      <div className="h-full 2extraL:flex 2extraL:space-x-5">
        <div className="h-fit XL:max-w-[300px] 2extraL:max-w-[250px] w-full rounded-xl border-primary">
          <FilterDrawer
            setFilterIsOpen={setFilterIsOpen}
            filterIsOpen={filterIsOpen}
            isLoading={brandsAreLoading && extractedCategoriesAreLoading}
          />
        </div>

        <div className="w-full min-h-full">{children}</div>
      </div>
    </div>
  );
}

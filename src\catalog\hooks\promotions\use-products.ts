import { useEffect } from "react";
import usePagination from "@/hooks/use-pagination";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { ProductType } from "../../types/products";
import { PaginationType } from "@/types";
import { retrievePromotionProductsFromServerSide } from "../../services/promotions/products-extraction";
import { CustomError } from "@/utils/custom-error";

export default function usePromotionProducts({
  limit,
  promotionSlug,
}: {
  limit: number;
  promotionSlug: string;
}) {
  const { page, setPage, pagesNumber, setPagesNumber, paginatedListRef } =
    usePagination();

  const { data, isLoading, error } = useQuery<
    {
      products: ProductType[];
      pagination: PaginationType;
    } | null,
    CustomError
  >({
    queryKey: ["promotion-products", page, promotionSlug],
    queryFn: () =>
      retrievePromotionProductsFromServerSide(page, limit, promotionSlug),
    placeholderData: keepPreviousData,
  });

  useEffect(() => {
    setPagesNumber(
      data?.pagination?.totalPages ? data?.pagination?.totalPages : 1
    );
  }, [data]);

  return {
    products: data?.products,
    productsAreLoading: isLoading,
    error,
    setPage,
    page,
    pagesNumber,
    paginatedListRef,
  };
}

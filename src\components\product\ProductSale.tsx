import React from 'react';
import {View, Text} from 'react-native';
import type {PropsWithChildren} from 'react';

import {theme} from '../../constants';
import {ProductType} from '../../types';

type Props = PropsWithChildren<{item: ProductType; containerStyle?: object}>;

const ProductSale: React.FC<Props> = ({
  item,
  containerStyle,
}): JSX.Element | null => {
  if (item.old_price) {
    return (
      <View
        style={{
          width: 39,
          height: 16,
          backgroundColor: theme.colors.white,
          borderWidth: 1,
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: 3,
          borderColor: theme.colors.lightBlue,
          ...containerStyle,
        }}
      >
        <Text
          style={{
            ...theme.fonts.DMSans_700Bold,
            fontSize: 8,
            textTransform: 'uppercase',
            lineHeight: 8 * 1.7,
            color: theme.colors.mainColor,
          }}
        >
          Sale
        </Text>
      </View>
    );
  }

  return null;
};

export default ProductSale;

import Image from "next/image";
import { CategoryType } from "../../types/categories";
import Text from "@/styles/text-styles";
import Link from "next/link";
import { getCategoryPageUrl } from "../../utils/urls";

interface Props {
  category: CategoryType;
}

export default function CategoryContainer({ category }: Props) {
  return (
    category && (
      <Link href={getCategoryPageUrl(category)} className="block">
        <div className="group w-full flex flex-col items-center gap-2 S:gap-3">
          <div className="w-full aspect-square overflow-hidden relative rounded-2xl bg-gradient-to-tr from-blue to-primary p-2 flex justify-center items-end">
            {category.image ? (
              <Image
                unoptimized
                width={120}
                height={120}
                alt={`${category.name}`}
                src={category.image}
              />
            ) : (
              <div className="w-full h-full flex justify-center items-center">
                <Image
                  unoptimized
                  width={102}
                  height={42}
                  alt={`Akal Logo`}
                  src={"/logos/company-white-logo.svg"}
                />
              </div>
            )}
          </div>
          <Text
            textStyle="TS7"
            className="text-center text-primary font-medium group-hover:text-primary line-clamp-1"
          >
            {category.name}
          </Text>
          <Text
            textStyle="TS8"
            className="text-gray text-center hidden S:block"
          >
            {category.numberOfProducts || 0} Produits
          </Text>
        </div>
      </Link>
    )
  );
}

import React from 'react';
import {ScrollView, TouchableOpacity, Text} from 'react-native';

import _v1 from './versions/_v1';
import _v2 from './versions/_v2';

import {theme, tabs} from '../../constants';
import {useAppSelector} from '../../hooks';
import {components} from '../../components';
import {statusBarHeight} from '../../utils';
import {useAppDispatch} from '../../hooks';

const Home: React.FC = (): JSX.Element => {
  const dispatch = useAppDispatch();
  const homeVersion = useAppSelector((state) => state.appState.homeVersion);

  const switchVersion = () => {
    return (
      <TouchableOpacity
        style={{
          position: 'absolute',
          backgroundColor: theme.colors.mainColor,
          padding: 10,
          zIndex: 1,
          borderTopRightRadius: 5,
          borderBottomRightRadius: 5,
          marginTop: statusBarHeight() + 70,
        }}
        onPress={() => {
          dispatch({
            type: 'appState/setHomeVersion',
            payload: homeVersion === 1 ? 2 : 1,
          });
        }}
      >
        <Text
          style={{
            color: theme.colors.white,
            ...theme.fonts.DMSans_400Regular,
          }}
        >
          swith version
        </Text>
      </TouchableOpacity>
    );
  };

  const renderStatusBar = (): JSX.Element => {
    return (
      <components.StatusBar
        backgroundColor={theme.colors.transparent}
        barStyle='dark-content'
      />
    );
  };

  const renderHeader = (): JSX.Element => {
    return (
      <components.Header burgerIcon={true} basket={true} bottomLine={true} />
    );
  };

  const renderTabBar = (): JSX.Element => {
    return (
      <components.TabBar>
        {tabs.map((item, index) => {
          return <components.TabBarItem item={item} key={index} />;
        })}
      </components.TabBar>
    );
  };

  const renderContent = (): JSX.Element => {
    return (
      <ScrollView contentContainerStyle={{flexGrow: 1}}>
        {homeVersion === 1 && <_v1 />}
        {homeVersion === 2 && <_v2 />}
      </ScrollView>
    );
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {switchVersion()}
      {renderHeader()}
      {renderContent()}
      {renderTabBar()}
    </components.SmartView>
  );
};

export default Home;

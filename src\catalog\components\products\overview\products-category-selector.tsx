"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Text from "@/styles/text-styles";
import ProductContainer from "../product/container/default";
import { HTMLAttributes, useState } from "react";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { Skeleton } from "@/components/ui/skeleton";
import { getCategoryPageUrl } from "@/modules/catalog/utils/urls";
import { CategoryType } from "@/modules/catalog/types/categories";
import useProducts from "@/modules/catalog/hooks/products/use-products";
import { useTranslations } from "next-intl";
import Image from "next/image";

interface Props extends HTMLAttributes<"html"> {
  categories?: CategoryType[];
}

export default function ProductsCategorySelector({
  categories,
  ...props
}: Props) {
  const t = useTranslations("landingPage.productsOverview");

  const [productsCategory, setProductsCategory] = useState(
    categories && categories.length > 0 ? categories[0] : null
  );
  const { products, productsAreLoading } = useProducts({
    limit: 4,
    categoriesSlugs: productsCategory
      ? productsCategory.subCategories.length === 0
        ? [productsCategory.slug]
        : productsCategory.subCategories
            .flatMap((cat) =>
              cat.subCategories.length > 0 ? cat.subCategories : [cat]
            )
            .map((cat) => cat.slug)
      : [],
  });

  const moreProductsLink = productsCategory
    ? getCategoryPageUrl(productsCategory)
    : "";

  return !productsAreLoading ? (
    products && (
      <div
        className={cn(
          "w-full flex flex-col items-center space-y-8",
          props.className
        )}
      >
        {/* Title */}
        <div className="w-full text-center">
          <Text textStyle="TS2" className="font-bold">
            {t("nosRecommendations")}
          </Text>
        </div>

        {categories && (
          <div className="mt-8">
            {/* Mobile horizontal scroll */}
            <div className="md:hidden overflow-x-auto pb-2">
              <div className="flex gap-3 min-w-max px-4">
                {categories.map((cat) => (
                  <Button
                    key={cat.id}
                    onClick={() => setProductsCategory(cat)}
                    variant="ghost"
                    className={`px-4 py-5 rounded-full text-sm border border-primary transition-colors whitespace-nowrap flex-shrink-0 ${
                      productsCategory !== null &&
                      cat.id === productsCategory.id
                        ? "bg-primary text-white"
                        : "bg-white text-primary hover:bg-primary hover:text-white"
                    }`}
                  >
                    <Text textStyle="TS6">{cat.name}</Text>
                  </Button>
                ))}
              </div>
            </div>
            {/* Desktop flex wrap */}
            <div className="hidden md:flex flex-wrap gap-3 justify-center">
              {categories.map((cat) => (
                <Button
                  key={cat.id}
                  variant="ghost"
                  onClick={() => setProductsCategory(cat)}
                  className={`px-7 py-5 rounded-full text-sm border border-primary transition-colors ${
                    productsCategory !== null && cat.id === productsCategory.id
                      ? "bg-primary text-white"
                      : "bg-white text-primary hover:bg-primary hover:text-white"
                  }`}
                >
                  <Text textStyle="TS6">{cat.name}</Text>
                </Button>
              ))}
            </div>
          </div>
        )}
        <div className="w-full grid regularL:grid-cols-4 grid-cols-2 gap-[10px]">
          {/* Promotional Container */}
          {productsCategory && productsCategory.bannerImage && (
            <Link href={getCategoryPageUrl(productsCategory)} className="block">
              <Image
                src={productsCategory.bannerImage}
                alt="AKAL logo"
                width={330}
                height={509}
                className="h-full object-cover rounded-lg hover:opacity-90 transition-opacity cursor-pointer"
              />
            </Link>
          )}

          {/* Products */}
          {products.slice(0, 3).map((product) => (
            <ProductContainer key={product.id} product={product} />
          ))}
        </div>
        <Link href={moreProductsLink}>
          <Button variant="voirplus" size="sm">
            <Text textStyle="TS5">{t("discoverPlus")}</Text>
          </Button>
        </Link>
      </div>
    )
  ) : (
    <ProductsOverviewSkeletons />
  );
}

export function ProductsOverviewSkeletons({
  className,
}: {
  className?: string;
}) {
  return (
    <div
      className={cn("w-full flex flex-col items-center space-y-8", className)}
    >
      {/* Title skeleton */}
      <Skeleton className="h-8 w-60" />

      {/* Category buttons skeleton */}
      {/* Mobile horizontal scroll skeleton */}
      <div className="md:hidden overflow-x-auto pb-2">
        <div className="flex gap-3 min-w-max px-4">
          {Array.from({ length: 3 }).map((_, idx) => (
            <Skeleton
              key={idx}
              className="h-12 w-24 rounded-full flex-shrink-0"
            />
          ))}
        </div>
      </div>
      {/* Desktop flex wrap skeleton */}
      <div className="hidden md:flex flex-wrap gap-3 justify-center">
        {Array.from({ length: 3 }).map((_, idx) => (
          <Skeleton key={idx} className="h-12 w-24 rounded-full" />
        ))}
      </div>

      {/* Products skeleton */}
      <div className="w-full grid regularL:grid-cols-4 grid-cols-2 gap-[10px]">
        {Array.from({ length: 8 }).map((_, idx) => (
          <ProductContainer key={idx} product={null} />
        ))}
      </div>

      {/* Button skeleton */}
      <Skeleton className="h-10 w-40 rounded-full" />
    </div>
  );
}

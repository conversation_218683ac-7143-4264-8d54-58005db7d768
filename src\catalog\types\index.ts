export type CriteriaType =
  | "priceAsc"
  | "priceDesc"
  | "createdAtDesc"
  | "createdAtAsc"
  | "displayOrder"
  | "nameAsc"
  | "nameDesc"
  | "mostSold";

export type ProductsSectionsVariant =
  | "default"
  | "news"
  | "similarProducts"
  | "productsByCategory"
  | "selection"
  | "recommended"
  | "mostSold";

export interface PromotionInResponse {
  id: string;
  slug: string;
  name: string;
}

export interface PromotionType {
  id: string;
  slug: string;
  name: string;
}

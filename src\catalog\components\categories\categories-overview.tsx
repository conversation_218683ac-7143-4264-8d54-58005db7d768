"use client";

import Text from "@/styles/text-styles";
import type { HTMLAttributes } from "react";
import { cn } from "@/lib/utils";
import CategoryContainer from "./category-container";
import { Skeleton } from "@/components/ui/skeleton";
import { useTranslations } from "next-intl";
import useCategories from "../../hooks/categories/use-categories";

interface Props extends HTMLAttributes<HTMLElement> {
  maxCategoriesNumber?: number;
}

export default function CategoriesOverview(props: Props) {
  const t = useTranslations("landingPage.categoriesOverview");
  const { categories, categoriesAreLoading } = useCategories();
  const maxCategories = props.maxCategoriesNumber || 8;

  return !(categories === undefined || categoriesAreLoading) ? (
    categories && (
      <section
        className={cn(
          "w-full flex flex-col space-y-4 px-4 sm:px-6 md:px-8",
          props.className
        )}
      >
        <div className="text-center mb-4 sm:mb-6 lg:mb-8">
          <h2>
            <Text
              textStyle="TS3"
              className="text-black font-bold text-xl sm:text-2xl md:text-3xl"
            >
              {t("title")}
            </Text>
          </h2>
          <p className="text-gray mt-1 sm:mt-2 max-w-2xl mx-auto text-xs sm:text-sm md:text-base">
            {t("attractivenessTitle")}
          </p>
        </div>

        <div className="relative">
          {/* Mobile scrollable view */}
          <div className="flex md:hidden overflow-x-auto pb-6 gap-4 sm:gap-6  w-full">
            <div className="flex gap-4 sm:gap-6 mx-auto">
              {categories.slice(0, maxCategories).map((category, idx) => (
                <div key={idx} className="flex-shrink-0 w-[100px] sm:w-[120px]">
                  <CategoryContainer category={category} />
                </div>
              ))}
            </div>
          </div>

          {/* Desktop grid view */}
          <div className="hidden md:grid grid-cols-4 lg:grid-cols-8 gap-4 md:gap-6 lg:gap-8">
            {categories.slice(0, maxCategories).map((category, idx) => (
              <div key={idx}>
                <CategoryContainer category={category} />
              </div>
            ))}
          </div>
        </div>
      </section>
    )
  ) : (
    <section
      className={cn(
        "w-full flex flex-col items-center space-y-8 px-4 sm:px-6 md:px-8",
        props.className
      )}
    >
      <div className="text-center mb-4 sm:mb-6 w-full">
        <h2 className="w-full">
          <Skeleton className="w-32 sm:w-40 h-8 sm:h-10 mx-auto" />
        </h2>
        <Skeleton className="w-3/4 h-4 sm:h-5 mx-auto mt-1 sm:mt-2 max-w-2xl" />
      </div>

      <div className="relative w-full">
        {/* Mobile skeleton */}
        <div className="flex md:hidden overflow-x-auto pb-6 gap-4 sm:gap-6 hide-scrollbar w-full">
          <div className="flex gap-4 sm:gap-6 mx-auto">
            {Array.from({ length: maxCategories }).map((_, idx) => (
              <div
                key={idx}
                className="flex-shrink-0 w-[100px] sm:w-[120px] flex flex-col gap-2"
              >
                <Skeleton className="w-full aspect-square rounded-2xl" />
                <Skeleton className="w-3/4 h-4 sm:h-5 mx-auto" />
                <Skeleton className="w-1/2 h-3 sm:h-4 mx-auto hidden sm:block" />
              </div>
            ))}
          </div>
        </div>

        {/* Desktop skeleton grid */}
        <div className="hidden md:grid grid-cols-4 lg:grid-cols-8 gap-4 md:gap-6 lg:gap-8">
          {Array.from({ length: maxCategories }).map((_, idx) => (
            <div key={idx} className="flex flex-col gap-2">
              <Skeleton className="w-full aspect-square rounded-2xl" />
              <Skeleton className="w-3/4 h-4 md:h-5 mx-auto" />
              <Skeleton className="w-1/2 h-3 md:h-4 mx-auto" />
            </div>
          ))}
        </div>

        {/* Pagination indicators for mobile */}
        <div className="absolute bottom-0 left-0 right-0 flex justify-center gap-1 md:hidden">
          <div className="w-1.5 h-1.5 rounded-full bg-primary opacity-70"></div>
          <div className="w-1.5 h-1.5 rounded-full bg-gray-300"></div>
          <div className="w-1.5 h-1.5 rounded-full bg-gray-300"></div>
        </div>
      </div>
    </section>
  );
}

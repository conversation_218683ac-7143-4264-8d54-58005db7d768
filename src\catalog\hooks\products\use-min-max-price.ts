import { useQuery } from "@tanstack/react-query";
import { PriceRange } from "../../types/products";
import { retrieveMinMaxPriceFromServerSide } from "../../services/products/min-max-price-extraction";

export default function useMinMaxPrice() {
  const { data, isLoading, isError } = useQuery<PriceRange | null>({
    queryKey: ["min-max-prices"],
    queryFn: () => retrieveMinMaxPriceFromServerSide(),
  });

  return {
    prices: data ? [data.minPrice, data.maxPrice] : [0, 1000],
    pricesAreLoading: isLoading,
    pricesError: isError,
  };
}

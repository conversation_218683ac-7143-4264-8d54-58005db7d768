"use client";
import ProductContainer from "../product/container/default";
import { HTMLAttributes } from "react";
import { cn } from "@/lib/utils";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import useProducts from "../../../hooks/products/use-products";
import { ProductsOverviewSkeletons } from "./default";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";

interface Props extends HTMLAttributes<"html"> {
  maxProductsNumber?: number;
  similarProductSlug: string;
}

export default function ProductSimilarProductsOverview({
  maxProductsNumber = 8,
  ...props
}: Props) {
  const t = useTranslations("productPage.similarProducts");
  const { products, productsAreLoading } = useProducts({
    limit: maxProductsNumber,
    similarProductSlug: props.similarProductSlug,
  });

  return products && !productsAreLoading ? (
    products.length > 0 ? (
      <div
        className={cn(
          "w-full bg-white flex flex-col items-center space-y-6 py-8",
          props.className
        )}
      >
        {/* Section Title */}
        <div className="text-center">
          <h3>
            <Text textStyle="TS2" className="text-gray-dark font-semibold">
              {t("title")}
            </Text>
          </h3>
          <Text textStyle="TS6" className="text-gray mt-2">
            {t("description")}
          </Text>
        </div>

        <Carousel
          opts={{ loop: true }}
          className="w-full flex flex-col space-y-4 items-center"
        >
          <CarouselContent className="py-[10px]">
            {products.map((_, carouselIdx) =>
              carouselIdx % 4 === 0 ? (
                <CarouselItem
                  key={carouselIdx}
                  className=" basis-full grid regularL:grid-cols-4 grid-cols-2 gap-[10px]"
                >
                  {Array.from({ length: 4 }).map((_, idx) =>
                    carouselIdx + idx >= products.length ? null : (
                      <ProductContainer
                        key={products[carouselIdx + idx].id}
                        product={products[carouselIdx + idx]}
                      />
                    )
                  )}
                </CarouselItem>
              ) : null
            )}
          </CarouselContent>
          <CarouselPrevious />
          <CarouselNext />
        </Carousel>
      </div>
    ) : null
  ) : (
    <ProductsOverviewSkeletons className={props.className} />
  );
}

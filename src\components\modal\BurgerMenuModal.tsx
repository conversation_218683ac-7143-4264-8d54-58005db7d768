import React from 'react';
import Modal from 'react-native-modal';
import {responsiveWidth} from 'react-native-responsive-dimensions';
import {View, Text, ScrollView, TouchableOpacity, Image} from 'react-native';

import Loader from '../Loader';
import {theme} from '../../constants';
import {useAppDispatch} from '../../hooks';
import {useAppNavigation} from '../../hooks';
import BurgerMenuItem from './BurgerMenuItem';
import {setScreen} from '../../store/slices/tabSlice';
import {useGetProductsQuery} from '../../store/slices/apiSlice';
import {statusBarHeight as RNstatusBarHeight} from '../../utils';

type Props = {
  showModal: boolean;
  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
};

const BurgerMenuModal: React.FC<Props> = ({
  showModal,
  setShowModal,
}): JSX.Element => {
  const navigation = useAppNavigation();
  const dispatch = useAppDispatch();

  const statusBarHeight = RNstatusBarHeight();

  const {
    data: productsData,
    error: productsError,
    isLoading: productsLoading,
  } = useGetProductsQuery();

  const products = productsData instanceof Array ? productsData : [];
  const sale = products?.filter((e) => e.old_price);
  const qty = sale?.length;

  const bestSellers = products?.filter((e) => e.types.includes('bestseller'));
  const featured = products?.filter((e) => e.types.includes('featured'));

  const ifLoading = () => {
    if (productsLoading) {
      return <Loader />;
    }
  };

  const renderCloseBtn = () => {
    return (
      <TouchableOpacity
        style={{
          alignSelf: 'flex-end',
          marginTop: 10,
          paddingHorizontal: 20,
          marginBottom: 30,
        }}
        onPress={() => setShowModal(false)}
      >
        {/* <svg.CloseMenuSvg /> */}
      </TouchableOpacity>
    );
  };

  const renderHeader = () => {
    return (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          paddingBottom: 20,
          borderBottomWidth: 1,
          paddingHorizontal: 20,
          marginBottom: 20,
          borderBottomColor: theme.colors.lightBlue,
        }}
      >
        <Image
          source={{
            uri: 'https://george-fx.github.io/kastelli/users/01.jpg',
          }}
          style={{
            width: responsiveWidth(14),
            aspectRatio: 1,
            marginRight: 14,
          }}
        />
        <View>
          <Text
            style={{
              ...theme.fonts.H5,
              color: theme.colors.mainColor,
              textTransform: 'capitalize',
            }}
            numberOfLines={1}
          >
            Callie Mosley
          </Text>
          <Text
            style={{
              ...theme.fonts.textStyle_14,
              color: theme.colors.textColor,
            }}
            numberOfLines={1}
          >
            <EMAIL>
          </Text>
        </View>
      </View>
    );
  };

  const renderMenu = () => {
    return (
      <View style={{paddingHorizontal: 20}}>
        <BurgerMenuItem
          version={1}
          title='>  Categories'
          containerStyle={{
            marginBottom: 6,
          }}
          onPress={() => {
            dispatch(setScreen('Search'));
            setShowModal(false);
          }}
        />
        <BurgerMenuItem
          version={1}
          title='>  Sale'
          quantity={qty}
          containerStyle={{
            marginBottom: 6,
          }}
          onPress={() => {
            setShowModal(false);
            navigation.navigate('Shop', {
              title: 'Sale',
              products: sale,
            });
          }}
        />
        <BurgerMenuItem
          version={1}
          title='>  New arrivals'
          quantity={0}
          containerStyle={{
            marginBottom: 6,
          }}
          onPress={() => {
            setShowModal(false);
            // navigation.navigate('Shop', {title: 'New arrivals'});
          }}
        />
        <BurgerMenuItem
          version={1}
          title='>  Best sellers'
          quantity={bestSellers?.length}
          containerStyle={{
            marginBottom: 6,
          }}
          onPress={() => {
            setShowModal(false);
            navigation.navigate('Shop', {
              title: 'Best sellers',
              products: bestSellers,
            });
          }}
        />
        <BurgerMenuItem
          version={1}
          title='>  Featured products'
          quantity={featured?.length}
          containerStyle={{
            marginBottom: 30,
          }}
          onPress={() => {
            setShowModal(false);
            navigation.navigate('Shop', {
              title: 'Featured products',
              products: featured,
            });
          }}
        />
      </View>
    );
  };

  const renderFooter = () => {
    return (
      <View style={{paddingLeft: 20}}>
        <BurgerMenuItem
          version={2}
          // icon={<svg.BellSvg />}
          title='Notifications'
          quantity={1}
          containerStyle={{marginBottom: 10}}
          onPress={() => {
            setShowModal(false);
            navigation.navigate('Notifications');
          }}
        />
        <BurgerMenuItem
          version={2}
          // icon={<svg.HelpCircleSvg />}
          title='Support'
          onPress={() => {
            console.log('Support');
          }}
        />
      </View>
    );
  };

  return (
    <Modal
      isVisible={showModal}
      onBackdropPress={() => setShowModal(false)}
      hideModalContentWhileAnimating={true}
      backdropTransitionOutTiming={0}
      style={{margin: 0}}
      animationIn='slideInLeft'
      animationOut='slideOutLeft'
    >
      <ScrollView
        style={{
          width: responsiveWidth(78),
          height: theme.sizes.height,
          backgroundColor: theme.colors.white,
          paddingTop: statusBarHeight,
          paddingBottom: 20,
        }}
        contentContainerStyle={{flexGrow: 1}}
      >
        {ifLoading()}
        {renderCloseBtn()}
        {renderHeader()}
        {renderMenu()}
        {renderFooter()}
      </ScrollView>
    </Modal>
  );
};

export default BurgerMenuModal;

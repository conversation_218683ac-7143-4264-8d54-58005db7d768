import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import { Progress } from "@/components/ui/progress";
import { HTMLAttributes } from "react";
import { cn } from "@/lib/utils";
import usePrices from "@/modules/cart/hooks/use-prices";
import calculateFreeShippingProgress from "@/modules/cart/utils/free-shipping-progress-calculation";

interface Props extends HTMLAttributes<"div"> {
  icon?: React.ReactNode;
  contentClassName?: string;
}

export default function ProductFreeShippingProgressBar(props: Props) {
  const t = useTranslations("shared.cart");
  const { subtotal, minAmountForFreeShipping } = usePrices();
  const freeShippingProgress = calculateFreeShippingProgress(
    minAmountForFreeShipping,
    subtotal
  );

  const investAmount = minAmountForFreeShipping - subtotal;

  return (
    <div className={cn("flex items-center space-x-4", props.className)}>
      <div className="text-primary w-10">{props.icon}</div>

      <div className="flex-1 flex flex-col gap-5">
        <div
          className={cn(
            "rounded-lg p-1 flex items-center gap-2",
            props.contentClassName
          )}
        >
          <Text textStyle="TS8" className="font-medium">
            {investAmount > 0
              ? t.rich("description", {
                  b: (chunk) => <span className="font-bold">{chunk}</span>,
                  price: () => (
                    <span className="font-bold">{`${investAmount.toFixed(
                      3
                    )} DT`}</span>
                  ),
                })
              : t("thankYou")}
          </Text>
        </div>

        <div
          className={cn("pr-4", {
            "pl-6": freeShippingProgress === 0,
          })}
        >
          <Progress
            progressbarclassname="from-primary to-[#3FCE82]"
            progressindicatorclassname="bg-primary rounded-[11px] py-0 px-2 -top-2"
            value={freeShippingProgress}
          />
        </div>
      </div>
    </div>
  );
}

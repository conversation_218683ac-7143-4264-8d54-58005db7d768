import { MetadataRoute } from "next";
import retrievePromotionsFromServerSide from "../../../services/promotions/promotions-extraction";

export async function getPromotionsPages(): Promise<MetadataRoute.Sitemap> {
  try {
    const promotions = await retrievePromotionsFromServerSide({});

    if (promotions) {
      const promotionsPages: MetadataRoute.Sitemap = promotions.map(
        (promotion) => ({
          url: `https://${process.env.FRONTEND_DOMAIN_NAME}/promotions/${promotion.slug}`,
          lastModified: new Date(),
          changeFrequency: "weekly",
          priority: 1,
        })
      );

      return promotionsPages;
    }
  } catch {
    return [];
  }

  return [];
}

import { ProductType as CatalogProductType } from '../types/products';
import { ProductType as AppProductType } from '../../types/ProductType';

// Adapter to convert catalog ProductType to app ProductType
export function adaptCatalogProductToApp(catalogProduct: CatalogProductType): AppProductType {
  const firstItem = catalogProduct.items[0];
  const firstPrice = firstItem?.prices[0];
  
  return {
    id: parseInt(catalogProduct.id) || 0,
    name: catalogProduct.name,
    price: firstPrice?.promotionalPrice || 0,
    rating: 4.5, // Default rating since catalog doesn't have this
    image: firstItem?.image || '',
    images: firstItem?.images?.join(',') || '',
    sizes: firstItem?.variations
      ?.filter(v => v.name.toLowerCase().includes('size'))
      ?.map(v => v.value) || [],
    size: firstItem?.variations
      ?.find(v => v.name.toLowerCase().includes('size'))?.value || '',
    colors: firstItem?.variations
      ?.filter(v => v.name.toLowerCase().includes('color'))
      ?.map(v => v.value) || [],
    color: firstItem?.variations
      ?.find(v => v.name.toLowerCase().includes('color'))?.value || '',
    description: catalogProduct.description || '',
    categories: catalogProduct.categoryIds.join(','),
    is_bestseller: false, // Default value
    is_featured: false, // Default value
    is_out_of_stock: !firstItem?.inStock,
    old_price: firstPrice?.realPrice !== firstPrice?.promotionalPrice 
      ? firstPrice?.realPrice 
      : undefined,
    quantity: 1, // Default quantity
    reviews: [], // Empty reviews array
    types: catalogProduct.categoryIds,
  };
}

// Adapter to convert app ProductType to catalog ProductType (if needed)
export function adaptAppProductToCatalog(appProduct: AppProductType): CatalogProductType {
  return {
    id: appProduct.id.toString(),
    slug: appProduct.name.toLowerCase().replace(/\s+/g, '-'),
    categoryIds: appProduct.types || [],
    brand: null, // App doesn't have brand info
    name: appProduct.name,
    description: appProduct.description,
    details: null,
    items: [{
      id: `item-${appProduct.id}`,
      barcode: '',
      reference: '',
      inStock: !appProduct.is_out_of_stock,
      image: appProduct.image,
      images: appProduct.images ? appProduct.images.split(',') : [],
      prices: [{
        currency: 'USD', // Default currency
        realPrice: appProduct.old_price || appProduct.price,
        promotionalPrice: appProduct.price,
      }],
      variations: [
        ...(appProduct.size ? [{
          id: 'size-1',
          name: 'Size',
          value: appProduct.size,
        }] : []),
        ...(appProduct.color ? [{
          id: 'color-1',
          name: 'Color',
          value: appProduct.color,
        }] : []),
      ],
      promotion: appProduct.old_price && appProduct.old_price > appProduct.price ? {
        amountReduced: (appProduct.old_price - appProduct.price).toString(),
        currency: 'USD',
        from: new Date().toISOString(),
        to: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
        type: 'percentage',
      } : undefined,
    }],
    metaContent: null,
  };
}

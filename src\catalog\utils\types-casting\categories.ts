import { toCamelCase } from "@/utils/text-transformer";
import { CategoryInResponseType, CategoryType } from "../../types/categories";
import { castToMetaContentType } from "@/utils/types-casting/meta-content";

export function castToCategoryType(
  categoryInResponse: CategoryInResponseType
): CategoryType {
  return {
    metaContent: castToMetaContentType(categoryInResponse.metaContent),
    slug: categoryInResponse.slug,
    name: toCamelCase(categoryInResponse.name),
    description: categoryInResponse.description as string,
    id: categoryInResponse.id,
    image: categoryInResponse.menuImage
      ? `${process.env.BACKEND_ADDRESS}${categoryInResponse.menuImage}`
      : null,
    bannerImage: categoryInResponse.bannerImage
      ? `${process.env.BACKEND_ADDRESS}${categoryInResponse.bannerImage}`
      : null,
    numberOfProducts: categoryInResponse.numberOfProducts || 0,
    subCategories: categoryInResponse.subCategories
      ? categoryInResponse.subCategories.map((subCategory) =>
          castToCategoryType(subCategory)
        )
      : [],
  };
}

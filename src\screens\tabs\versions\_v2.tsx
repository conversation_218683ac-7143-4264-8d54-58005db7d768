import React from 'react';
import {
  View,
  ScrollView,
  TouchableOpacity,
  Text,
  ImageBackground,
} from 'react-native';

import {text} from '../../../text';
import {theme} from '../../../constants';
import {ProductType} from '../../../types';
import {useAppDispatch} from '../../../hooks';
import {components} from '../../../components';
import {useAppNavigation} from '../../../hooks';
import {setTag} from '../../../store/slices/tagSlice';
import {setScreen} from '../../../store/slices/tabSlice';
import {
  useGetTagsQuery,
  useGetBannersQuery,
  useGetProductsQuery,
} from '../../../store/slices/apiSlice';

const _v2 = () => {
  const dispatch = useAppDispatch();
  const navigation = useAppNavigation();

  const {
    data: productsData,
    error: productsError,
    isLoading: productsLoading,
  } = useGetProductsQuery();

  const {
    data: tagsData,
    error: tagsError,
    isLoading: tagsLoading,
  } = useGetTagsQuery();

  const {
    data: bannersData,
    error: bannersError,
    isLoading: bannersLoading,
  } = useGetBannersQuery();

  if (productsLoading || bannersLoading) {
    return <components.Loader />;
  }

  const tags = tagsData instanceof Array ? tagsData : [];
  const products = productsData instanceof Array ? productsData : [];

  const renderBanner_v1 = (): JSX.Element | null => {
    const banner = bannersData instanceof Array ? bannersData[0] : {};
    const sale = products?.filter((e: ProductType) => e.old_price);

    if (!banner || !bannersData) {
      return null;
    }

    return (
      <TouchableOpacity
        style={{
          marginRight: 20,
          marginBottom: 50,
          marginTop: 20,
          borderTopRightRadius: 5,
          borderBottomRightRadius: 5,
        }}
        onPress={() =>
          navigation.navigate('Shop', {title: 'Sale', products: sale})
        }
      >
        <ImageBackground
          source={{uri: banner.image}}
          style={{
            width: '100%',
            aspectRatio: 1.42,
            justifyContent: 'center',
            borderTopRightRadius: 5,
            borderBottomRightRadius: 5,
          }}
          imageStyle={{
            borderTopRightRadius: 5,
            borderBottomRightRadius: 5,
            backgroundColor: theme.colors.imageBackground,
          }}
          resizeMode='cover'
        >
          <View style={{paddingHorizontal: 20}}>
            <text.H2>{banner.title_line_1}</text.H2>
            <text.H2 style={{marginBottom: 20}}>{banner.title_line_2}</text.H2>
            <components.ShopNow text={banner.button_text} />
          </View>
        </ImageBackground>
      </TouchableOpacity>
    );
  };

  const renderFeatured = (): JSX.Element | null => {
    const featured =
      products?.filter((e: ProductType) => e.types.includes('featured')) ?? [];
    const slice = featured?.slice(0, 4);

    if (!featured?.length || !featured) {
      return null;
    }

    return (
      <View style={{marginBottom: 50}}>
        <components.BlockHeading
          title='Featured'
          containerStyle={{paddingHorizontal: 20}}
          onPress={() =>
            navigation.navigate('Shop', {
              title: 'Featured',
              products: featured,
            })
          }
        />
        <ScrollView
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{paddingLeft: 20}}
          decelerationRate={0}
        >
          {slice?.map(
            (item: ProductType, index: number, array: ProductType[]) => {
              const lastItem = index === array.length - 1;
              return (
                <components.ProductCard
                  key={index}
                  item={item}
                  version={2}
                  lastItem={lastItem}
                />
              );
            },
          )}
        </ScrollView>
      </View>
    );
  };

  const renderTopCategories = (): JSX.Element | null => {
    const blockWidth = (theme.sizes.width - 40) / 3.3;
    const slice = tags?.slice(0, 6);

    if (!tags?.length || !tags) {
      return null;
    }

    return (
      <View
        style={{
          marginHorizontal: 20,
          marginBottom: 50,
        }}
      >
        <components.BlockHeading
          title='Top categories'
          onPress={() => {
            dispatch(setScreen('Search'));
          }}
        />
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            flexWrap: 'wrap',
          }}
        >
          {slice?.map((item, index, array) => {
            const qty = products.filter((e) =>
              e.tags.includes(item.name),
            ).length;
            return (
              <TouchableOpacity
                key={item.id}
                style={{
                  width: blockWidth,
                }}
                onPress={() => {
                  dispatch(setTag(item.name));
                  dispatch(setScreen('Search'));
                }}
              >
                <ImageBackground
                  source={{uri: item.image}}
                  style={{
                    width: blockWidth,
                    height: 90,
                    padding: 10,
                    justifyContent: 'space-between',
                    marginRight: index === array.length - 1 ? 20 : 14,
                    marginBottom: 14,
                  }}
                  imageStyle={{
                    borderRadius: 5,
                  }}
                  resizeMode='cover'
                >
                  <View
                    style={{
                      borderWidth: 1,
                      alignSelf: 'flex-start',
                      borderColor: theme.colors.lightBlue,
                      backgroundColor: theme.colors.white,
                      borderRadius: 3,
                      paddingHorizontal: 3,
                    }}
                  >
                    <Text
                      style={{
                        fontSize: 8,
                        textTransform: 'uppercase',
                        lineHeight: 8 * 1.5,
                        color: theme.colors.textColor,
                        ...theme.fonts.DMSans_400Regular,
                      }}
                    >
                      {qty}
                    </Text>
                  </View>
                  <Text
                    style={{
                      ...theme.fonts.DMSans_400Regular,
                      color: theme.colors.mainColor,
                      fontSize: 10,
                      lineHeight: 10 * 1.5,
                      textTransform: 'capitalize',
                    }}
                  >
                    {item.name}
                  </Text>
                </ImageBackground>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  };

  const renderBestSellers = (): JSX.Element | null => {
    const bestSellers =
      products?.filter((e: ProductType) => e.types.includes('bestseller')) ??
      [];

    const slice = bestSellers?.slice(0, 5);

    if (!bestSellers?.length || !bestSellers) {
      return null;
    }

    return (
      <View
        style={{
          marginBottom: 50,
          marginLeft: 20,
        }}
      >
        <components.BlockHeading
          title='Best Sellers'
          containerStyle={{
            paddingRight: 20,
          }}
          onPress={() => {
            navigation.navigate('Shop', {
              products: bestSellers,
              title: 'Best Sellers',
            });
          }}
        />
        {slice?.map(
          (item: ProductType, index: number, array: ProductType[]) => {
            const lastItem = index === array.length - 1;
            return (
              <components.ProductCard
                key={index}
                item={item}
                version={3}
                lastItem={lastItem}
              />
            );
          },
        )}
      </View>
    );
  };

  const renderBanner_v2 = (): JSX.Element | null => {
    const banner = bannersData instanceof Array ? bannersData[2] : {};
    const sale = products?.filter((e: ProductType) => e.old_price);

    if (!banner || !bannersData) {
      return null;
    }

    return (
      <TouchableOpacity
        style={{
          marginRight: 20,
          marginBottom: 50,
          marginTop: 20,
          borderTopRightRadius: 5,
          borderBottomRightRadius: 5,
        }}
        onPress={() =>
          navigation.navigate('Shop', {title: 'Sale', products: sale})
        }
      >
        <ImageBackground
          source={{uri: banner.image}}
          style={{
            width: '100%',
            aspectRatio: 1.775,
            borderTopRightRadius: 5,
            borderBottomRightRadius: 5,
          }}
          imageStyle={{
            borderTopRightRadius: 5,
            borderBottomRightRadius: 5,
            backgroundColor: theme.colors.imageBackground,
          }}
          resizeMode='cover'
        >
          <View style={{paddingHorizontal: 20, paddingTop: 30}}>
            <text.H2>{banner.title_line_1}</text.H2>
            <text.H2 style={{marginBottom: 20}}>{banner.title_line_2}</text.H2>
            <components.ShopNow text={banner.button_text} />
          </View>
        </ImageBackground>
      </TouchableOpacity>
    );
  };

  return (
    <components.SmartView>
      {renderBanner_v1()}
      {renderFeatured()}
      {renderTopCategories()}
      {renderBestSellers()}
      {renderBanner_v2()}
    </components.SmartView>
  );
};

export default _v2;

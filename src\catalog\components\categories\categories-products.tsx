"use client";
import useCategories from "../../hooks/categories/use-categories";
import ProductsCategorySelector from "../products/overview/products-category-selector";
import { ProductsOverviewSkeletons } from "../products/overview/default";

export default function CategoriesProducts() {
  const { categories, categoriesAreLoading } = useCategories();

  return !categoriesAreLoading ? (
    categories && categories.length > 0 ? (
      <ProductsCategorySelector categories={categories} />
    ) : null
  ) : (
    <ProductsOverviewSkeletons />
  );
}

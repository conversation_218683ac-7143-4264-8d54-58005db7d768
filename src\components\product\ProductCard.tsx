import React, {memo, PropsWithChildren} from 'react';
import {TouchableOpacity, View, ImageBackground} from 'react-native';

import {text} from '../../text';
import {theme} from '../../constants';
import {ProductType} from '../../types';
import ProductSale from './ProductSale';
import ProductName from './ProductName';
import ProductPrice from './ProductPrice';
import ProductRating from './ProductRating';
import ProductInCart from './ProductInCart';
import {useAppNavigation} from '../../hooks';
import ProductInWishlist from './ProductInWishlist';

type Props = PropsWithChildren<{
  item: ProductType;
  version: number;
  lastItem?: boolean;
}>;

const ProductCard: React.FC<Props> = ({
  version,
  item,
  lastItem,
}): JSX.Element | null => {
  const navigation = useAppNavigation();

  // home v.1 > Best Sellers
  if (version === 1) {
    return (
      <TouchableOpacity
        style={{
          width: 200,
          marginRight: lastItem ? 20 : 14,
        }}
        onPress={() => navigation.navigate('Product', {item})}
      >
        <ImageBackground
          source={{uri: item.image}}
          style={{
            marginBottom: 14,
            width: 200,
            height: 250,
          }}
          imageStyle={{
            backgroundColor: theme.colors.imageBackground,
            borderRadius: 5,
          }}
          resizeMode='contain'
        >
          <ProductInCart
            item={item}
            version={1}
            containerStyle={{
              position: 'absolute',
              right: 0,
              bottom: 0,
              padding: 14,
            }}
          />
          <ProductInWishlist
            item={item}
            version={1}
            containerStyle={{
              position: 'absolute',
              right: 0,
              top: 0,
              padding: 14,
            }}
          />
        </ImageBackground>
        <ProductName item={item} version={1} style={{marginBottom: 4}} />
        <ProductPrice item={item} version={1} />
      </TouchableOpacity>
    );
  }

  // Home v.2 > Featured Products
  if (version === 2) {
    return (
      <TouchableOpacity
        style={{
          width: 138,
          height: 'auto',
          marginBottom: 20,
          marginRight: lastItem ? 20 : 14,
        }}
        onPress={() => navigation.navigate('Product', {item})}
      >
        <ImageBackground
          source={{uri: item.image}}
          style={{
            marginBottom: 14,
            width: '100%',
            height: 170,
          }}
          imageStyle={{
            backgroundColor: theme.colors.imageBackground,
            borderRadius: 5,
          }}
          resizeMode='cover'
        >
          <ProductInWishlist
            item={item}
            containerStyle={{
              position: 'absolute',
              right: 0,
              top: 0,
              padding: 10,
            }}
            version={1}
          />
          <ProductInCart
            item={item}
            version={1}
            containerStyle={{
              position: 'absolute',
              right: 0,
              bottom: 0,
              alignItems: 'center',
              padding: 12,
            }}
          />
          <ProductSale
            item={item}
            containerStyle={{
              position: 'absolute',
              left: 10,
              bottom: 10,
            }}
          />
        </ImageBackground>
        <ProductName item={item} version={1} />
        <ProductPrice item={item} version={1} />
      </TouchableOpacity>
    );
  }

  // Home v.2 > Best Sellers
  if (version === 3) {
    const marginBottom = lastItem ? 0 : 10;

    return (
      <TouchableOpacity
        style={{flexDirection: 'row', marginBottom: marginBottom}}
        onPress={() => navigation.navigate('Product', {item})}
      >
        <ImageBackground
          source={{uri: item.image}}
          style={{width: 100, height: 100}}
          imageStyle={{
            backgroundColor: theme.colors.imageBackground,
            borderRadius: 3,
          }}
          resizeMode='cover'
        >
          <ProductSale
            item={item}
            containerStyle={{
              position: 'absolute',
              left: 10,
              bottom: 10,
            }}
          />
        </ImageBackground>
        <View
          style={{
            flex: 1,
            paddingLeft: 14,
            paddingRight: 20,
            borderTopWidth: 1,
            borderBottomWidth: 1,
            justifyContent: 'center',
            borderColor: theme.colors.lightBlue,
          }}
        >
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <text.T14 style={{marginBottom: 3}} numberOfLines={1}>
              {item.name}
            </text.T14>
            <ProductInWishlist item={item} version={1} />
          </View>
          <ProductPrice
            item={item}
            containerStyle={{marginBottom: 11}}
            version={1}
          />
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <ProductRating item={item} version={1} />
            <ProductInCart item={item} version={1} />
          </View>
        </View>
      </TouchableOpacity>
    );
  }

  return null;
};

export default memo(ProductCard);

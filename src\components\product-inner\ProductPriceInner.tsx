import React, {PropsWithChildren} from 'react';
import {View, Text, TouchableOpacity} from 'react-native';

import {svg} from '../../assets/svg';
import {theme} from '../../constants';
import {ProductType} from '../../types';
import {quantityInCart} from '../../utils';
import {useAppDispatch, useAppSelector} from '../../hooks';
import {removeFromCart, addToCart} from '../../store/slices/cartSlice';

type Props = PropsWithChildren<{item: ProductType}>;

const ProductPriceInner: React.FC<Props> = ({item}): JSX.Element => {
  const dispatch = useAppDispatch();

  const renderPrice = () => {
    return (
      <View style={{flexDirection: 'row', alignItems: 'flex-end'}}>
        {item.old_price && (
          <Text
            style={{
              ...theme.fonts.DMSans_400Regular,
              marginRight: 10,
              fontSize: 16,
              lineHeight: 16 * 1.7,
              textDecorationLine: 'line-through',
              color: theme.colors.textColor,
            }}
          >
            ${item.old_price.toFixed(1)}
          </Text>
        )}
        <Text
          style={{
            ...theme.fonts.DMSans_700Bold,
            fontSize: 20,
            lineHeight: 20 * 1.4,
          }}
        >
          ${item.price.toFixed(1)}
        </Text>
      </View>
    );
  };

  const renderQuantity = () => {
    return (
      <View
        style={{
          marginLeft: 'auto',
          flexDirection: 'row',
          alignItems: 'center',
        }}
      >
        <TouchableOpacity
          style={{padding: 20}}
          onPress={() => dispatch(removeFromCart(item))}
        >
          <svg.CounterMinusSvg />
        </TouchableOpacity>
        <View
          style={{
            width: 25,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Text
            style={{
              ...theme.fonts.DMSans_700Bold,
              fontSize: 14,
              color: theme.colors.textColor,
            }}
          >
            {quantityInCart(item)}
          </Text>
        </View>
        <TouchableOpacity
          style={{padding: 20}}
          onPress={() => dispatch(addToCart(item))}
        >
          <svg.CounterPlusSvg />
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View
      style={{
        marginLeft: 20,
        borderLeftWidth: 1,
        borderTopWidth: 1,
        borderBottomWidth: 1,
        borderTopColor: '#DBE9F5',
        borderBottomColor: '#DBE9F5',
        borderLeftColor: '#DBE9F5',
        borderTopLeftRadius: 3,
        borderBottomLeftRadius: 3,
        paddingLeft: 20,
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 6,
        marginBottom: 30,
      }}
    >
      {renderPrice()}
      {renderQuantity()}
    </View>
  );
};

export default ProductPriceInner;

"use client";

import { useTranslations } from "next-intl";
import { Skeleton } from "@/components/ui/skeleton";
import ProductContainer from "./product/container/default";
import PaginationMangement from "@/components/pagination/pagination-management";
import Text from "@/styles/text-styles";
import useProducts from "../../hooks/products/use-products";
import { ProductsSectionsVariant } from "../../types";
import { getCriteriaBasedOnProductsVariant } from "../../utils/criteria-based-on-variant";

interface Props {
  type: ProductsSectionsVariant;
  similarProductSlug?: string;
}
export default function ProductsLists({ type, similarProductSlug }: Props) {
  const t = useTranslations("filtersPage");

  const {
    products,
    productsAreLoading,
    setPage,
    page,
    pagesNumber,
    paginatedListRef: scrollViewPortRef,
  } = useProducts({
    limit: 20,
    criteria: getCriteriaBasedOnProductsVariant(type),
    similarProductSlug,
  });

  return (
    <>
      <div ref={scrollViewPortRef} className="L:p-4 w-full">
        {productsAreLoading ? (
          <div className="w-full grid grid-cols-1 M:grid-cols-2 regularL:grid-cols-3 XL:grid-cols-4 gap-2 L:gap-5 transition-all">
            {Array.from({ length: 12 }).map((_, idx) => (
              <div
                key={idx}
                className="w-full rounded-[20px] flex flex-col justify-between space-y-3 p-2"
              >
                {/* Image Skeleton */}
                <div className="w-full flex-1 flex justify-center items-center">
                  <Skeleton className="h-[300px] w-[300px] rounded-XL" />
                </div>

                {/* Product Details Skeleton */}
                <div className="flex items-center space-x-2">
                  <div className="flex-1 max-w-[80%] flex flex-col space-y-2">
                    {/* Product Name Skeleton */}
                    <Skeleton className="h-4 w-full" />

                    {/* Price Details Skeleton */}
                    <div className="flex space-x-2 items-center">
                      <Skeleton className="h-4 w-[50px]" />
                      <Skeleton className="h-4 w-[40px]" />
                    </div>
                  </div>

                  {/* Cart Icon Skeleton */}
                  <Skeleton className="w-[35px] h-[35px] rounded-full" />
                </div>
              </div>
            ))}
          </div>
        ) : products && products.length > 0 ? (
          <div className="w-full grid grid-cols-1 S:grid-cols-2 regularL:grid-cols-3 XL:grid-cols-4 gap-2 L:gap-5 transition-all">
            {products.map((product, idx) => (
              <ProductContainer key={idx} product={product} />
            ))}
          </div>
        ) : (
          <div className="h-[450px] min-w-full flex items-center justify-center">
            <p className="text-[50px] text-center">
              <Text textStyle="TS2">{t.raw("NoProducts")}</Text>
            </p>
          </div>
        )}
      </div>
      <div className="flex justify-center mt-4">
        <PaginationMangement
          currentPage={page}
          pagesNumber={pagesNumber}
          changePage={setPage}
        />
      </div>
    </>
  );
}

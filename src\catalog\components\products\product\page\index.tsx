"use client";
import { screenWrapperStyle } from "@/components/screen-wrapper";
import { cn } from "@/lib/utils";
import useProduct from "@/modules/catalog/hooks/products/use-product";
import Text, { TextStyle } from "@/styles/text-styles";
import { notFound } from "next/navigation";
import { useEffect } from "react";
import { useTranslations } from "next-intl";
import ProductCarousel from "./product-carousel";
import ProductDetails from "./product-details";
import ProductPurchaseConfigurator from "./product-purchase-configurator";
import { Package, Truck } from "lucide-react";
import BreadCrumb from "@/components/breadcrumb";
import { Separator } from "@/components/ui/separator";
import DOMPurify from "dompurify";
import ProductsOverview from "../../overview/default";

interface Props {
  slug: string;
}

export default function ProductInfoPage({ slug }: Props) {
  const { product, productIsLoading, productItem, error } = useProduct(slug);
  const t = useTranslations("productPage");

  useEffect(() => {
    if (error && error.status === 404) notFound();
  }, [error]);

  return !(product === undefined || productIsLoading) ? (
    product && productItem ? (
      <div className="w-full flex flex-col items-center regularL:space-y-6 space-y-4 ">
        {/* Breadcrumbs */}
        <div className={cn("w-full", screenWrapperStyle)}>
          <BreadCrumb />
        </div>

        <div
          className={cn(
            "flex regularL:flex-row flex-col regularL:justify-center regularL:space-y-0 regularL:space-x-7  space-y-4",
            screenWrapperStyle
          )}
        >
          {/* Product Images */}
          <ProductCarousel
            className="regularL:basis-[40%] "
            productItem={productItem}
          />

          <div className="regularL:basis-[60%] max-w-[600px] regularL:space-y-4 space-y-3 ">
            <ProductDetails product={product} productItem={productItem} />
            <ProductPurchaseConfigurator
              productItem={{
                slug: product.slug,
                id: productItem.id,
                name: product.name,
                productId: product.id,
                image: productItem.image,
                prices: productItem.prices,
                variations: productItem.variations,
                inStock: productItem.inStock,
                cartQuantity: 1,
              }}
            />

            {/* Product Specifications Section */}
            <div className="space-y-3">
              <div className="space-y-2 text-sm text-gray">
                <div className="flex gap-3">
                  <span className="text-blue-dead">
                    {t("specifications.codeabarre")}:
                  </span>
                  <span className="text-black">
                    {product.items[0].barcode ||
                      t("specifications.notAvailable")}
                  </span>
                </div>
                <div className="flex gap-3">
                  <span className="text-blue-dead">
                    {t("specifications.reference")}:
                  </span>
                  <span className="text-black">
                    {product.items[0].reference ||
                      t("specifications.notAvailable")}
                  </span>
                </div>
                <div className="flex gap-3">
                  <span className="text-blue-dead">
                    {t("specifications.brand")}:
                  </span>
                  <span className="text-black">
                    {product.brand?.name || t("specifications.notAvailable")}
                  </span>
                </div>
                <div className="flex gap-3">
                  <span className="text-blue-dead">
                    {t("specifications.availability")}:
                  </span>
                  <span className="text-black">
                    {productItem.inStock
                      ? t("specifications.inStock")
                      : t("specifications.outOfStock")}
                  </span>
                </div>
                {productItem.variations.length > 0 && (
                  <div className="flex gap-3">
                    <span className="text-blue-dead">
                      {t("specifications.variations")}:
                    </span>
                    <span className="text-black">
                      {productItem.variations
                        .map((variation) => variation.value)
                        .join(", ")}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Additional Product Information */}
            <div className="space-y-3 pt-3">
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div className="flex flex-col items-start p-4 bg-gray/10 rounded-xl">
                  <div className="flex items-center gap-2 mb-2">
                    <Truck className="w-5 h-5 text-gray-dark" />
                    <span className="text-gray-dark font-bold">
                      {t("delivery.shipping")}
                    </span>
                  </div>
                  <span className="text-gray-dark text-xs">
                    {t.rich("delivery.freeShipping", {
                      amount: (chunks) => <strong>{chunks}</strong>,
                    })}
                  </span>
                </div>
                <div className="flex flex-col items-start p-4 bg-gray/10 rounded-xl">
                  <div className="flex items-center gap-2 mb-2">
                    <Package className="w-5 h-5 text-gray-dark" />
                    <span className="text-gray-dark font-bold">
                      {t("delivery.returns")}
                    </span>
                  </div>
                  <span className="text-gray-dark text-xs">
                    {t.rich("delivery.returnPolicy", {
                      hours: (chunks) => <strong>{chunks}</strong>,
                    })}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Product Description Section */}
        {product.details && (
          <>
            <div className={cn("w-full", screenWrapperStyle)}>
              <div className="space-y-6 py-8">
                <div className="space-y-4">
                  <div className="space-y-4 text-gray">
                    <div
                      className={cn(
                        "text-gray prose prose-sm",
                        TextStyle["TS7"]
                      )}
                      dangerouslySetInnerHTML={{
                        __html: DOMPurify.sanitize(product.details as string),
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
            <Separator />
          </>
        )}

        {/* Similar Products overview section */}
        <ProductsOverview
          similarProductSlug={product.slug}
          className={screenWrapperStyle}
          variant="similarProducts"
        />
      </div>
    ) : (
      <div className="w-full min-h-[400px] flex justify-center items-center">
        <Text textStyle="TS3" className="font-bold text-gray">
          {t("noProductsFound")}
        </Text>
      </div>
    )
  ) : (
    // Skeletons
    <div className="w-full flex flex-col items-center regularL:space-y-8 space-y-6 mt-4 S:mt-6 L:mt-8">
      {/* Breadcrumbs */}
      <div className={cn("w-full", screenWrapperStyle)}>
        <BreadCrumb />
      </div>

      <div
        className={cn(
          "w-full flex regularL:flex-row flex-col regularL:space-y-0 regularL:space-x-8 space-y-6",
          screenWrapperStyle
        )}
      >
        {/* Product Images */}
        <ProductCarousel className="regularL:basis-[40%]" productItem={null} />

        <div className="regularL:basis-[60%] regularL:space-y-8 space-y-6">
          <ProductDetails productItem={null} />
          <ProductPurchaseConfigurator productItem={null} />
        </div>
      </div>

      <div
        className={cn(
          "w-full bg-gray-50 flex justify-center border-t border-gray-200"
        )}
      >
        <ProductsOverview
          similarProductSlug={slug}
          className={screenWrapperStyle}
        />
      </div>
    </div>
  );
}

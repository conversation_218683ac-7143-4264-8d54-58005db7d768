import React from 'react';
import {TouchableOpacity, ImageBackground} from 'react-native';
import {responsiveWidth} from 'react-native-responsive-dimensions';

import {text} from '../../text';
import {theme} from '../../constants';
import {useAppNavigation} from '../../hooks';
import QuantityBadge from '../badges/QuantityBadge';
import {CategoryType, ProductType} from '../../types';

type Props = {
  data: ProductType[];
  item: CategoryType;
  lastElement?: boolean;
  selectedCategory?: CategoryType;
  setSelectedCategory?: React.Dispatch<React.SetStateAction<CategoryType>>;
};

const CategoryItem: React.FC<Props> = ({item, data}): JSX.Element | null => {
  // const navigation = useAppNavigation();

  const blockWidth = responsiveWidth(100) / 2 - 30;
  const blockHeight = responsiveWidth(100) / 2 - 30;

  const getProductsByCategory = (category: string): number => {
    const products = data instanceof Array ? data : [];
    const categories = products.map((item: ProductType) => item.categories);
    const filteredCategories = categories.filter(
      (item: string) => item === category,
    );
    // const filteredProducts = products.filter(
    //   (item: ProductType) => item.categories === category,
    // );
    return filteredCategories.length;
    // return 0;
  };

  const myCategory = getProductsByCategory('Dresses');
  console.log('myCategory', myCategory);

  const containerStyle = {
    width: blockWidth,
    height: blockHeight,
    marginBottom: 14,
    borderRadius: 5,
    backgroundColor: theme.colors.imageBackground,
  };

  return (
    <TouchableOpacity style={{...containerStyle}}>
      <ImageBackground
        style={{
          width: blockWidth,
          height: blockHeight,
          borderRadius: 10,
          paddingHorizontal: 14,
          paddingTop: 14,
          paddingBottom: 12,
          justifyContent: 'space-between',
        }}
        source={{uri: item.image}}
        imageStyle={{borderRadius: 5}}
      >
        <QuantityBadge
          quantity={getProductsByCategory(item.name)}
          version={2}
        />
        <text.T14 numberOfLines={1}>{item.name}</text.T14>
      </ImageBackground>
    </TouchableOpacity>
  );
};

export default CategoryItem;

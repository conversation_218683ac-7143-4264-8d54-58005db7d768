"use client";
import type { ProductType } from "../../../../types/products";
import Text from "@/styles/text-styles";
import { formatPrice } from "../../../../utils/prices-transformation";
import Link from "next/link";
import { Skeleton } from "@/components/ui/skeleton";
import { useTranslations } from "next-intl";
import { getPromotionPercentage } from "../../../../utils/promotion-percentage";
import { getBrandPageUrl } from "@/modules/catalog/utils/urls";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import useCurrency from "@/modules/catalog/hooks/use-currency";
import { useCartStore } from "@/modules/cart/store/cart-store";
import { useWishlistStore } from "@/modules/wishlist/store/wishlist-store";
import HeartButton from "@/components/ui/heart-button";
import useProductUrl from "@/modules/catalog/hooks/products/use-product-url";
import { cn } from "@/lib/utils";

interface Props {
  product: ProductType | null;
}

export default function ProductContainer({ product }: Props) {
  const catalogT = useTranslations("catalog.product");
  const {
    actions: { addProductItem: addProductItemToCart },
  } = useCartStore();
  const {
    actions: {
      toggleProductItem,
      isProductInWishlist: checkIsProductInWishlist,
    },
  } = useWishlistStore();
  const { currency } = useCurrency();

  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  const productPageUrl = useProductUrl(product);
  const promotionPercentage = product
    ? getPromotionPercentage(
        product.items[0].prices[0].realPrice,
        product.items[0].prices[0].promotionalPrice
      )
    : 0;

  const isProductInWishlist = product
    ? checkIsProductInWishlist(product.items[0].id)
    : false;

  // Get available images
  const availableImages =
    product?.items[0]?.images?.filter((img) => img && img.trim() !== "") || [];
  const primaryImage =
    product?.items[0]?.image || availableImages[0] || "/logos/akal-logo.svg";
  const hasMultipleImages = availableImages.length > 1;

  // Enhanced hover handlers
  const handleMouseEnter = () => {
    setIsHovered(true);

    if (hasMultipleImages) {
      setCurrentImageIndex(1);
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setCurrentImageIndex(0);
  };

  // Touch handlers for mobile
  const handleTouchStart = () => {
    if (hasMultipleImages) {
      setIsHovered(true);
      setCurrentImageIndex(currentImageIndex === 0 ? 1 : 0);
    }
  };

  useEffect(() => {
    setCurrentImageIndex(0);
    setIsHovered(false);
  }, [product]);

  const handleWishlistToggle = () => {
    if (product) {
      toggleProductItem({
        slug: product.slug,
        id: `temp-${product.items[0].id}`,
        productItemId: product.items[0].id,
        productId: product.id,
        name: product.name,
        prices: product.items[0].prices,
        image: product.items[0].image,
        variations: product.items[0].variations,
        inStock: product.items[0].inStock,
      });
    }
  };

  return product ? (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden h-full flex flex-col group hover:shadow-lg transition-all duration-300">
      <div
        className="relative overflow-hidden"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onTouchStart={handleTouchStart}
      >
        {/* Discount Badge */}
        {promotionPercentage !== "0" && (
          <div className="absolute top-4 left-4 z-20 transform transition-all duration-300 group-hover:scale-105">
            <div className="bg-danger text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
              -{promotionPercentage}%
            </div>
          </div>
        )}

        {/* Wishlist Button */}
        <HeartButton
          size="sm"
          variant="default"
          isLiked={isProductInWishlist}
          className="absolute top-4 right-4 z-20 transform transition-all duration-300 group-hover:scale-105"
          onClick={handleWishlistToggle}
        />

        {/* Image indicators for multiple images */}
        {hasMultipleImages && (
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-20 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            {availableImages.slice(0, 2).map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  currentImageIndex === index ? "bg-white" : "bg-white/50"
                }`}
              />
            ))}
          </div>
        )}

        {/* Product Image Container */}
        <Link href={productPageUrl} className="block">
          <div className="aspect-square w-full relative z-10 overflow-hidden bg-gray-50">
            {/* Primary Image */}
            <img
              src={primaryImage || "/placeholder.svg"}
              alt={product.name}
              className={`
                absolute inset-0 w-full h-full object-cover
                transition-all duration-500 ease-in-out
                ${
                  isHovered && hasMultipleImages
                    ? "opacity-0 scale-105"
                    : "opacity-100 scale-100"
                }
                group-hover:scale-105
              `}
            />

            {/* Secondary Image (for hover effect) */}
            {hasMultipleImages && availableImages[1] && (
              <img
                src={availableImages[1] || "/placeholder.svg"}
                alt={`${product.name}`}
                className={`
                  absolute inset-0 w-full h-full object-cover
                  transition-all duration-500 ease-in-out
                  ${isHovered ? "opacity-100 scale-100" : "opacity-0 scale-95"}
                `}
              />
            )}

            {/* Overlay effect on hover */}
            <div
              className={`
              absolute inset-0 bg-black/5 transition-opacity duration-300
              ${isHovered ? "opacity-100" : "opacity-0"}
            `}
            />
          </div>
        </Link>

        {/* Quick add button overlay (appears on hover) */}
        <div
          className={cn(
            "absolute inset-x-4 bottom-4 z-20 transform transition-all duration-300 ease-out L:block hidden ",
            {
              "translate-y-0 opacity-100": isHovered,
              "translate-y-4 opacity-0": !isHovered,
            }
          )}
        >
          <Button
            className="w-full rounded-full bg-white/95 backdrop-blur-sm text-black hover:bg-primary hover:text-white border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              addProductItemToCart({
                slug: product.slug,
                id: product.items[0].id,
                productId: product.id,
                cartQuantity: 1,
                name: product.name,
                prices: product.items[0].prices,
                image: product.items[0].image,
                variations: product.items[0].variations,
                inStock: product.items[0].inStock,
              });
            }}
          >
            <Text textStyle="TS6" className="font-medium">
              {catalogT("quickAdd")}
            </Text>
          </Button>
        </div>
      </div>

      {/* Product Info */}
      <div className="px-6 pt-4 border-t-2 border-[#EFF0F2] flex flex-col flex-grow h-[140px]">
        {/* Brand Name */}
        {product.brand && (
          <Link href={getBrandPageUrl(product.brand.slug)}>
            <Text
              textStyle="TS7"
              className="text-gray/80 mb-1 hover:text-primary transition-colors duration-200"
            >
              {product.brand.name}
            </Text>
          </Link>
        )}

        {/* Product Name */}
        <Link href={productPageUrl}>
          <div className="h-[48px] overflow-hidden">
            <Text
              textStyle="TS6"
              className="text-gray-900 font-bold line-clamp-2 leading-6 text-ellipsis overflow-hidden hover:text-primary transition-colors duration-200"
              style={{
                display: "-webkit-box",
                WebkitLineClamp: 2,
                WebkitBoxOrient: "vertical",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              {product.name}
            </Text>
          </div>
        </Link>

        {/* Prices */}
        <div className="flex extraL:flex-row flex-col items-baseline L:gap-2 gap-1">
          <Text textStyle="TS6" className="text-secondary font-semibold">
            {`${formatPrice(
              product.items[0].prices[0].promotionalPrice
            )} ${currency}`}
          </Text>
          {promotionPercentage !== "0" && (
            <Text
              textStyle="TS8"
              className="text-gray-400 line-through font-medium"
            >
              {`${formatPrice(
                product.items[0].prices[0].realPrice
              )} ${currency}`}
            </Text>
          )}
        </div>
      </div>

      {/* Mobile Quick Add Button */}
      <div className="px-6 pb-4 mt-auto L:hidden flex">
        <Button
          className="w-full rounded-full bg-white text-black hover:bg-primary hover:text-white border border-[#EFF0F2] shadow-[0px_12px_45.9px_0px_rgba(0,0,0,0.05)] transition-all duration-300"
          onClick={() =>
            addProductItemToCart({
              slug: product.slug,
              id: product.items[0].id,
              productId: product.id,
              cartQuantity: 1,
              name: product.name,
              prices: product.items[0].prices,
              image: product.items[0].image,
              variations: product.items[0].variations,
              inStock: product.items[0].inStock,
            })
          }
        >
          <Text textStyle="TS6">{catalogT("quickAdd")}</Text>
        </Button>
      </div>
    </div>
  ) : (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-100 h-full flex flex-col">
      <div className="aspect-square w-full">
        <Skeleton className="w-full h-full" />
      </div>
      <div className="px-6 py-4 border-t-2 border-[#EFF0F2] flex flex-col flex-grow h-[140px]">
        <Skeleton className="h-4 w-20 mb-1" />
        <div className="h-[48px] mb-2">
          <Skeleton className="h-5 w-full mb-1" />
          <Skeleton className="h-5 w-3/4" />
        </div>
        <Skeleton className="h-5 w-24 mb-4" />
        <div className="mt-auto">
          <Skeleton className="h-10 w-full rounded-full" />
        </div>
      </div>
    </div>
  );
}

import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { BrandType } from "../../types/brands";
import { retrieveBrandsFromServerSide } from "../../services/brands/brands-extraction";
import usePagination from "@/hooks/use-pagination";
import { useEffect } from "react";
import { PaginationType } from "@/types";
import { usePathname } from "next/navigation";

interface Params {
  categoriesSlugs?: string[];
  productPriceRange?: number[];
  searchByProductName?: string;
  productInStock?: boolean;
  brandsSlugs?: string[];
  productSlugs?: string[];
  search?: string;
  limit: number;
  paginationAffectUrl?: boolean;
}

export default function useBrands({
  limit,
  paginationAffectUrl,
  categoriesSlugs,
  productPriceRange,
  searchByProductName,
  productInStock,
  brandsSlugs,
  productSlugs,
  search,
}: Params) {
  const pathname = usePathname();
  const { page, setPage, pagesNumber, setPagesNumber } = usePagination({
    paginationAffectUrl: paginationAffectUrl ? paginationAffectUrl : false,
  });

  const { data, isLoading, isError } = useQuery<{
    brands: BrandType[];
    pagination: PaginationType;
  } | null>({
    queryKey: [
      "brands",
      pathname,
      page,
      limit,
      categoriesSlugs,
      productPriceRange,
      searchByProductName,
      productInStock,
      brandsSlugs,
      productSlugs,
      search,
    ],
    queryFn: () =>
      retrieveBrandsFromServerSide({
        page,
        limit,
        categoriesSlugs,
        productPriceRange,
        searchByProductName,
        productInStock,
        brandsSlugs,
        productSlugs,
        search,
      }),
    placeholderData: keepPreviousData,
  });

  useEffect(() => {
    if (data?.pagination && pagesNumber !== data?.pagination?.totalPages)
      setPagesNumber(data.pagination.totalPages);
  }, [data]);

  return {
    brands: data?.brands || [],
    brandsAreLoading: isLoading,
    brandsError: isError,
    setPage,
    page,
    pagesNumber,
  };
}

import { QueryClient } from '@tanstack/react-query';

// Create a separate query client for catalog module
// This ensures it doesn't interfere with RTK Query
export const catalogQueryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Cache for 5 minutes
      staleTime: 5 * 60 * 1000,
      // Keep in cache for 10 minutes
      gcTime: 10 * 60 * 1000,
      // Retry failed requests 2 times
      retry: 2,
      // Don't refetch on window focus by default
      refetchOnWindowFocus: false,
      // Don't refetch on reconnect by default
      refetchOnReconnect: false,
    },
    mutations: {
      // Retry failed mutations once
      retry: 1,
    },
  },
});

// Query keys for catalog module
export const catalogQueryKeys = {
  products: (filters?: any) => ['catalog', 'products', filters],
  similarProducts: (productSlug: string, page: number) => 
    ['catalog', 'similar-products', productSlug, page],
  categories: () => ['catalog', 'categories'],
  brands: () => ['catalog', 'brands'],
} as const;

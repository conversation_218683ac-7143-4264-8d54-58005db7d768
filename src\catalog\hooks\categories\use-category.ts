import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { retrieveCategoryFromServerSide } from "../../services/categories/category-extraction";
import { CategoryType } from "../../types/categories";

interface Params {
  categorySlug: string;
}

export default function useCategory({ categorySlug }: Params) {
  const { data, isLoading } = useQuery<CategoryType | null>({
    queryKey: ["category", categorySlug],
    queryFn: () => retrieveCategoryFromServerSide({ categorySlug }),
    placeholderData: keepPreviousData,
  });

  return {
    categoryIsLoading: isLoading,
    category: data,
  };
}

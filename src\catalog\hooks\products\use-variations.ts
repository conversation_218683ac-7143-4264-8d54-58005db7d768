import { useQuery } from "@tanstack/react-query";
import { GroupedVariationType } from "../../types/products";
import { retrieveVariationsFromServerSide } from "../../services/variations/variations-extraction";

interface Params {
  productSlug?: string;
  categoriesSlugs?: string[];
  brandSlugs?: string[];
  search?: string;
}

export default function useVariations({
  productSlug,
  categoriesSlugs,
  brandSlugs,
  search,
}: Params = {}) {
  const { data, isLoading, isError } = useQuery<GroupedVariationType[] | null>({
    queryKey: ["variations", productSlug, categoriesSlugs, brandSlugs, search],
    queryFn: () =>
      retrieveVariationsFromServerSide({
        productSlug,
        categoriesSlugs,
        brandSlugs,
        search,
      }),
  });

  return {
    variations: data || [],
    variationsAreLoading: isLoading,
    variationsError: isError,
  };
}

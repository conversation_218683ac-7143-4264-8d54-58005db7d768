import React from 'react';
import {ScrollView, TouchableOpacity, Text} from 'react-native';

import _v1 from './versions/_v1';
import _v2 from './versions/_v2';

import {theme} from '../../constants';
import {components} from '../../components';
import {statusBarHeight} from '../../utils';
import type {RootStackParamList} from '../../types';
import {useAppSelector, useAppDispatch} from '../../hooks';
import {setProductVersion} from '../../store/slices/appStateSlice';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';

type Props = NativeStackScreenProps<RootStackParamList, 'Product'>;

const Product: React.FC<Props> = ({route}): JSX.Element => {
  const dispatch = useAppDispatch();

  const {item} = route.params;
  const productVersion = useAppSelector(
    (state) => state.appState.productVersion,
  );

  const switchVersion = () => {
    return (
      <TouchableOpacity
        style={{
          position: 'absolute',
          backgroundColor: theme.colors.mainColor,
          padding: 10,
          zIndex: 1,
          borderTopRightRadius: 5,
          borderBottomRightRadius: 5,
          marginTop: statusBarHeight() + 70,
        }}
        onPress={() => {
          // dispatch({
          //   type: 'appState/setHomeVersion',
          //   payload: productVersion === 1 ? 2 : 1,
          // });
          dispatch(setProductVersion(productVersion === 1 ? 2 : 1));
        }}
      >
        <Text
          style={{
            color: theme.colors.white,
            ...theme.fonts.DMSans_400Regular,
          }}
        >
          swith version
        </Text>
      </TouchableOpacity>
    );
  };

  const renderStatusBar = () => {
    return <components.StatusBar />;
  };

  const renderHeader = () => {
    return <components.Header basket={true} goBack={true} />;
  };

  const renderContent = () => {
    return (
      <ScrollView
        contentContainerStyle={{flexGrow: 1, paddingBottom: 20}}
        showsVerticalScrollIndicator={false}
      >
        {productVersion === 1 && <_v1 item={item} />}
        {productVersion === 2 && <_v2 item={item} />}
      </ScrollView>
    );
  };

  const renderHomeIndicator = () => {
    return <components.HomeIndicator />;
  };

  return (
    <components.SmartView>
      {switchVersion()}
      {renderStatusBar()}
      {renderHeader()}
      {renderContent()}
      {renderHomeIndicator()}
    </components.SmartView>
  );
};

export default Product;

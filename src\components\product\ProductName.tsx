import React from 'react';
import {Text, TextStyle, View} from 'react-native';

import {theme} from '../../constants';
import {ProductType} from '../../types';

type Props = {item: ProductType; style?: TextStyle; version: 1 | 2};

const ProductName: React.FC<Props> = ({item, style}): JSX.Element | null => {
  return (
    <Text
      style={{
        marginRight: 'auto',
        color: theme.colors.textColor,
        ...theme.fonts.textStyle_14,
        ...style,
      }}
      numberOfLines={1}
    >
      {item.name}
    </Text>
  );
  return null;
};

export default ProductName;

import { Checkbox } from "@/components/ui/checkbox";

import { useProductsFilteringStore } from "@/modules/catalog/store/products-filter";
import { CategorySelectionType } from "@/modules/catalog/types/categories";
import { useTranslations } from "next-intl";
import FilterChoiceContainer from "./filter-choice-container";
import Text from "@/styles/text-styles";

export default function SubCategoriesSelectionDropDown() {
  const t = useTranslations("filtersPage");
  const { categories, setCategories } = useProductsFilteringStore();

  const handleSubCategoryClick = (
    parentCategory: CategorySelectionType,
    choosedSubCategory: CategorySelectionType
  ) => {
    const category = categories.find((cat) => cat.id === parentCategory.id);

    if (category) {
      const subCategory = category.subCategories.find(
        (subCat) => subCat.id === choosedSubCategory.id
      );

      if (subCategory) {
        if (subCategory.selected) {
          subCategory.selected = false;
        } else {
          subCategory.selected = true;
        }

        setCategories([...categories]);
      }
    }
  };

  return (
    <FilterChoiceContainer title={t.raw("subCategoriesHeader")}>
      <ul className="space-y-1">
        {categories.flatMap((category, index) =>
          category.selected
            ? category.subCategories.map((subCategory) => (
                <li
                  key={subCategory.id}
                  className="flex items-center space-x-3"
                >
                  <Checkbox
                    id={subCategory.id}
                    className="w-4 h-4"
                    checked={subCategory.selected}
                    onCheckedChange={() =>
                      handleSubCategoryClick(category, subCategory)
                    }
                  />
                  <label
                    htmlFor={subCategory.id}
                    className="text-sm text-gray cursor-pointer"
                  >
                    <Text textStyle="TS7">{subCategory.name}</Text>
                  </label>
                </li>
              ))
            : []
        )}
      </ul>
    </FilterChoiceContainer>
  );
}

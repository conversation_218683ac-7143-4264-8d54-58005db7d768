import { cn } from "@/lib/utils";
import Text, { TextStyle } from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import { ItemType, ProductType } from "../../../../types/products";
import { formatPrice } from "../../../../utils/prices-transformation";
import { Skeleton } from "@/components/ui/skeleton";
import DOMPurify from "dompurify";
import { Separator } from "@/components/ui/separator";
import useCurrency from "@/modules/catalog/hooks/use-currency";

interface Props {
  product?: ProductType;
  productItem: ItemType | null;
}

export default function ProductDetails({ product, productItem }: Props) {
  const t = useTranslations("productPage");
  const { currency } = useCurrency();
  const promotionIsAvailable =
    productItem?.prices[0].realPrice !==
    productItem?.prices[0].promotionalPrice;

  return product && productItem ? (
    <div className="space-y-4">
      {/* Product Name and barcode for reference section */}
      <div className="space-y-2">
        <Text textStyle="TS7" className="text-blue-dead">
          {product.brand?.name}
        </Text>
        <h1 className="font-medium text-gray-dark">
          <Text textStyle="TS2">{product.name}</Text>
        </h1>
        <div
          className={cn("text-gray prose prose-sm", TextStyle["TS7"])}
          dangerouslySetInnerHTML={{
            __html: DOMPurify.sanitize(product.description as string),
          }}
        ></div>
      </div>

      {/* Prices Section */}
      <div className="flex flex-col space-y-1">
        <Text textStyle="TS3" className="text-gray-dark font-semibold">
          {`${formatPrice(
            product.items[0].prices[0].promotionalPrice
          )} ${currency}`}
        </Text>
        {promotionIsAvailable && (
          <Text textStyle="TS6" className="text-gray line-through">
            {`${formatPrice(product.items[0].prices[0].realPrice)} ${currency}`}
          </Text>
        )}
      </div>

      <Separator />
    </div>
  ) : (
    <div className="space-y-8">
      <div className="space-y-2">
        <Skeleton className="h-12 w-64" />
        <Skeleton className="h-5 w-32" />
      </div>

      <div className="flex flex-col space-y-2">
        <Skeleton className="h-6 w-24" />
        <Skeleton className="h-8 w-32" />
      </div>

      <div className="space-y-3">
        <Skeleton className="h-5 w-20" />
        <Skeleton className="h-16 w-full" />
      </div>

      {/* Size Selector */}
      <div className="space-y-3">
        <Skeleton className="h-5 w-24" />
        <div className="flex gap-3">
          {Array.from({ length: 3 }).map((_, idx) => (
            <Skeleton key={idx} className="h-10 w-16" />
          ))}
        </div>
      </div>
    </div>
  );
}

# Catalog Module Integration

This document describes the integration of the Next.js catalog module with the React Native app.

## Overview

The catalog module has been successfully integrated with the React Native app while maintaining both Redux (app) and Zustand (catalog) state management systems working harmoniously.

## Architecture

### State Management Coexistence
- **Redux**: Continues to handle app-wide state (cart, wishlist, user auth, navigation)
- **Zustand**: Handles catalog-specific state (product filters, search, pagination)
- **TanStack Query**: Handles catalog data fetching alongside RTK Query

### Key Components

#### 1. HTTP Layer
- `src/catalog/lib/http-methods.ts` - React Native compatible HTTP client
- Uses fetch API instead of axios for better React Native compatibility
- Configured to use the same base URL as the main app

#### 2. State Management
- `src/catalog/store/products-filter-rn.ts` - Zustand store for catalog filtering
- `src/catalog/lib/query-client.ts` - Separate TanStack Query client

#### 3. Data Services
- `src/catalog/services/products/products-extraction-rn.ts` - Product fetching service
- `src/catalog/services/products/similar-products-extraction-rn.ts` - Similar products service
- `src/catalog/utils/types-casting/products-rn.ts` - Type casting utilities

#### 4. React Native Components
- `src/catalog/components/products/product/container/react-native.tsx` - Product card component
- `src/catalog/components/products/products-list-rn.tsx` - Products list component
- `src/screens/CatalogProducts.tsx` - Main products screen

#### 5. Type Adapters
- `src/catalog/utils/type-adapters.ts` - Bridges catalog and app product types

## Usage

### Navigation
Access the catalog products screen from the Shop screen by tapping "Browse Catalog Products" button, or navigate programmatically:

```typescript
navigation.navigate('CatalogProducts');
```

### Adding Products to Cart
Products from the catalog are automatically converted to app-compatible format when added to cart:

```typescript
const handleAddToCart = (catalogProduct: CatalogProductType) => {
  const appProduct = adaptCatalogProductToApp(catalogProduct);
  dispatch(addToCart(appProduct));
};
```

### Filtering and Search
Use the Zustand store for catalog-specific filtering:

```typescript
const {
  selectedBrands,
  categories,
  search,
  setSearch,
  addBrand,
  removeBrand,
} = useProductsFilteringStoreRN();
```

## Performance Considerations

### Memory Management
- Separate query clients prevent interference between RTK Query and TanStack Query
- Proper cache configuration with 5-minute stale time and 10-minute garbage collection
- Memoized components to prevent unnecessary re-renders

### Network Optimization
- Pagination with configurable page size (default: 10 items)
- Proper error handling and retry logic
- Optimistic updates for better UX

### State Optimization
- Zustand store only manages catalog-specific state
- Redux store continues to handle app-wide state
- No conflicts between the two state management systems

## Configuration

### API Endpoints
Update the base URL in `src/catalog/lib/http-methods.ts` if needed:

```typescript
const BASE_URL = 'https://kastelli.rn-admin.site/api';
```

### Image URLs
Update the backend address in `src/catalog/utils/types-casting/products-rn.ts`:

```typescript
const BACKEND_ADDRESS = 'https://kastelli.rn-admin.site';
```

## Future Enhancements

1. **Advanced Filtering**: Add more filter options (price range, ratings, etc.)
2. **Infinite Scroll**: Implement infinite scrolling for better UX
3. **Offline Support**: Add offline caching for products
4. **Search Suggestions**: Implement search autocomplete
5. **Product Comparison**: Add product comparison feature

## Troubleshooting

### Common Issues

1. **Network Errors**: Check API base URL configuration
2. **Type Errors**: Ensure type adapters are properly configured
3. **State Conflicts**: Verify Zustand and Redux stores are not interfering
4. **Image Loading**: Check backend address for image URLs

### Debug Tools

- TanStack Query DevTools available in development
- Redux DevTools for app state debugging
- React Native Debugger for component inspection

## Testing

The integration has been tested for:
- ✅ State management coexistence
- ✅ Data fetching and caching
- ✅ Component rendering and navigation
- ✅ Cart integration
- ✅ Error handling
- ✅ Performance optimization

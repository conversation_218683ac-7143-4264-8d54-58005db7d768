import { SeoMetaContentType, SeoMetaContentTypeInResponse } from "@/types";

export interface CategoryType {
  name: string;
  image: string | null;
  bannerImage: string | null;
  id: string;
  slug: string;
  description?: string | null;
  subCategories: CategoryType[];
  metaContent: SeoMetaContentType | null;
  numberOfProducts?: number;
}
export interface CategoryInResponseType {
  id: string;
  slug: string;
  name: string;
  description?: string | null;
  menuImage: string | null;
  bannerImage: string | null;
  createdAt?: string;
  subCategories: CategoryInResponseType[];
  metaContent: SeoMetaContentTypeInResponse | null;
  numberOfProducts?: number;
}

//used in filter page
export interface CategorySelectionType {
  name: string;
  image: string | null;
  id: string;
  slug: string;
  selected: boolean;
  subCategories: CategorySelectionType[];
}

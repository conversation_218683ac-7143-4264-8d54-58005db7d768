import {
  Drawer,
  DrawerContent,
  DrawerDes<PERSON>,
  DrawerHeader,
  Drawer<PERSON>it<PERSON>,
  DrawerTrigger,
} from "@/components/ui/drawer";
import useScreenSize from "@/hooks/use-screen-size";
import { Dispatch, SetStateAction } from "react";
import FilterOptions from "./filter-options";

interface Props {
  filterIsOpen: boolean;
  setFilterIsOpen: Dispatch<SetStateAction<boolean>>;
  isLoading: boolean;
}

export default function FilterDrawer({
  setFilterIsOpen,
  filterIsOpen,
  isLoading = false,
}: Props) {
  const { width } = useScreenSize();
  const doubleExtraL = 900;

  return width < doubleExtraL ? (
    <>
      <Drawer open={filterIsOpen} onOpenChange={setFilterIsOpen}>
        <DrawerTrigger />
        <DrawerContent className="">
          <FilterOptions
            setFilterIsOpen={setFilterIsOpen}
            isLoading={isLoading}
            filterHeaderIsUsed={true}
          />
          <DrawerHeader>
            <DrawerTitle></DrawerTitle>
          </DrawerHeader>
          <DrawerDescription></DrawerDescription>
        </DrawerContent>
      </Drawer>
    </>
  ) : (
    <FilterOptions
      setFilterIsOpen={setFilterIsOpen}
      isLoading={isLoading}
      criteriaDropDownIsUsed={false}
    />
  );
}

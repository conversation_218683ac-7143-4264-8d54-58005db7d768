import React, {useState} from 'react';
import {Alert, View, TouchableOpacity, ScrollView, Image} from 'react-native';

import {svg} from '../../../assets/svg';
import {theme} from '../../../constants';
import {ProductType} from '../../../types';
import {useAppDispatch} from '../../../hooks';
import {quantityInCart} from '../../../utils';
import {components} from '../../../components';
import {addedToCartMessage} from '../../../utils';
import {useGetColorsQuery} from '../../../store/slices/apiSlice';
import {addToCart, fullRemoveFromCart} from '../../../store/slices/cartSlice';

type Props = {item: ProductType};

const _v2: React.FC<Props> = ({item}): JSX.Element => {
  const dispatch = useAppDispatch();
  const quantity = quantityInCart(item) as number;
  const images = JSON.parse(item.images) as string[];

  const {
    data: colorsData,
    error: colorsError,
    isLoading: colorsIsLoading,
  } = useGetColorsQuery();

  const [selectedColor, setSelectedColor] = useState<string>('');
  const [currentSlideIndex, setCurrentSlideIndex] = useState<number>(0);
  const [selectedSize, setSelectedSize] = useState<string>(item.sizes[1]);

  console.log('selectedColor >>', selectedColor);

  const names = item.colors as string[];
  const dataColors = colorsData instanceof Array ? colorsData : [];
  const colors = dataColors?.filter((item) => names.includes(item.name));

  if (colorsIsLoading) {
    return <components.Loader />;
  }

  const updateCurrentSlideIndex = (e: any): void => {
    const contentOffsetX = e.nativeEvent.contentOffset.x;
    const currentIndex = Math.round(contentOffsetX / theme.sizes.width);
    setCurrentSlideIndex(currentIndex);
  };

  const renderCarousel = (): JSX.Element => {
    const renderImages = () => {
      return (
        <ScrollView
          onMomentumScrollEnd={updateCurrentSlideIndex}
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          pagingEnabled={true}
          style={{
            position: 'absolute',
            right: 0,
            left: 0,
            top: 0,
            bottom: 0,
          }}
          contentContainerStyle={{backgroundColor: '#F5F9FC'}}
          bounces={false}
          alwaysBounceHorizontal={false}
        >
          {images.map((image, index) => {
            return (
              <Image
                key={index}
                source={{uri: image}}
                style={{
                  width: theme.sizes.width,
                  height: '100%',
                }}
                resizeMode='contain'
              />
            );
          })}
        </ScrollView>
      );
    };

    const renderInWishlist = () => {
      return (
        <components.InWishlist
          item={item}
          version={2}
          containerStyle={{position: 'absolute', right: 23, top: 23, zIndex: 1}}
        />
      );
    };

    const renderBadge = () => {
      return (
        <components.SaleBadge
          item={item}
          version={2}
          containerStyle={{
            marginTop: 20,
            marginLeft: 20,
          }}
        />
      );
    };

    const renderDots = () => {
      return (
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
            position: 'absolute',
            bottom: 31,
            left: 0,
            right: 0,
          }}
        >
          {images.map((_, index) => (
            <View
              key={index}
              style={{
                width: 10,
                height: 10,
                marginHorizontal: 5,
                borderRadius: 50,
                backgroundColor:
                  currentSlideIndex === index
                    ? theme.colors.mainColor
                    : theme.colors.white,
                borderColor:
                  currentSlideIndex === index
                    ? theme.colors.mainColor
                    : theme.colors.lightBlue,
                borderWidth: 1,
              }}
            />
          ))}
        </View>
      );
    };

    const renderColorSelect = () => {
      return (
        <View style={{position: 'absolute', right: 0, bottom: 0, margin: 20}}>
          {colors?.map((item, index) => {
            return (
              <TouchableOpacity
                onPress={() => setSelectedColor(item.name)}
                key={index}
                style={{
                  borderRadius: 5,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: 10,
                  width: 30,
                  height: 30,
                  backgroundColor: item.hex,
                }}
              >
                {selectedColor === item.name && <svg.ColorSelectSvg />}
              </TouchableOpacity>
            );
          })}
        </View>
      );
    };

    const renderSizeSelect = () => {
      return (
        <View style={{position: 'absolute', left: 0, bottom: 0, margin: 20}}>
          {item.sizes.map((item, index, array) => {
            const lastElement = index === array.length - 1;

            return (
              <components.ProductSize
                key={index}
                item={item}
                selectedSize={selectedSize}
                setSelectedSize={setSelectedSize}
                containerStyle={{
                  marginBottom: lastElement ? 0 : 10,
                }}
              />
            );
          })}
        </View>
      );
    };

    return (
      <View style={{width: theme.sizes.width, aspectRatio: 0.91}}>
        {renderImages()}
        {renderInWishlist()}
        {renderBadge()}
        {renderDots()}
        {renderColorSelect()}
        {renderSizeSelect()}
      </View>
    );
  };

  const renderTabs = () => {
    return <components.ProductTab item={item} />;
  };

  const renderNameWithRating = () => {
    return <components.ProductNameInner item={item} />;
  };

  const renderPriceWithQuantity = () => {
    return <components.ProductPriceInner item={item} />;
  };

  const renderButton = () => {
    return (
      <components.Button
        title='+ ADd to cart'
        containerStyle={{margin: 20}}
        onPress={() => {
          if (quantity > 0) {
            Alert.alert(
              'Item already in cart',
              'Do you want to add another one?',
              [
                {
                  text: 'Cancel',
                  style: 'cancel',
                },
                {
                  text: 'OK',
                  onPress: () => {
                    dispatch(fullRemoveFromCart(item));
                    dispatch(
                      addToCart({
                        ...item,
                        color: selectedColor,
                        size: selectedSize,
                      }),
                    );
                    addedToCartMessage(item);
                  },
                },
              ],
              {cancelable: false},
            );
            return;
          }
          dispatch(
            addToCart({
              ...item,
              color: selectedColor,
              size: selectedSize,
            }),
          );
          addedToCartMessage(item);
        }}
      />
    );
  };

  return (
    <components.SmartView>
      {renderCarousel()}
      {renderTabs()}
      {renderNameWithRating()}
      {renderPriceWithQuantity()}
      {renderButton()}
    </components.SmartView>
  );
};

export default _v2;

"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Text from "@/styles/text-styles";
import ProductContainer from "../product/container/default";
import { HTMLAttributes, useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { getBrandPageUrl } from "@/modules/catalog/utils/urls";
import { BrandType } from "@/modules/catalog/types/brands";
import useProducts from "@/modules/catalog/hooks/products/use-products";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { Skeleton } from "@/components/ui/skeleton";
import useScreenSize, { useProductSwiper } from "@/hooks/use-screen-size";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

interface Props extends HTMLAttributes<"html"> {
  brands?: BrandType[];
}

export default function ProductsBrandSelector({ brands, ...props }: Props) {
  const t = useTranslations("landingPage.brandsOverview");
  const { width: screenWidth } = useScreenSize();

  const [selectedBrand, setSelectedBrand] = useState<BrandType | null>(null);

  const moreProductsLink = selectedBrand
    ? getBrandPageUrl(selectedBrand.slug)
    : "";

  // Filter brands that have products
  const brandsWithProducts =
    brands?.filter((brand) => brand.numberOfProducts > 0) || [];

  // Initialize selected brand when brands prop changes
  useEffect(() => {
    if (brandsWithProducts && brandsWithProducts.length > 0 && !selectedBrand) {
      setSelectedBrand(brandsWithProducts[0]);
    }
  }, [brandsWithProducts, selectedBrand]);

  const { products, productsAreLoading } = useProducts({
    limit: screenWidth < 740 ? 5 : 7,
    queryKeys: [screenWidth],

    brandSlugs: selectedBrand ? [selectedBrand.slug] : [],
  });

  const {
    isMobile,
    shouldUseSwiper,
    showPromotionalContainer,
    isSingleProduct,
  } = useProductSwiper(products?.length || 0);

  const renderProductsGrid = () => (
    <div
      className={cn(
        "grid gap-[10px]",
        isMobile && isSingleProduct
          ? "grid-cols-1 max-w-[280px] mx-auto"
          : "regularL:grid-cols-4 grid-cols-2"
      )}
    >
      {showPromotionalContainer && selectedBrand && selectedBrand.image && (
        <Link
          href={
            selectedBrand
              ? getBrandPageUrl(selectedBrand.slug)
              : "/produits/filtres"
          }
          className="block"
        >
          <Image
            src={selectedBrand?.image || "/logos/akal-logo.svg"}
            alt={`${selectedBrand?.name || "AKAL"} brand`}
            width={330}
            height={509}
            className="h-full object-cover rounded-lg hover:opacity-90 transition-opacity cursor-pointer"
          />
        </Link>
      )}

      {/* Products */}
      {products?.map((product) => (
        <ProductContainer key={product.id} product={product} />
      ))}
    </div>
  );

  const renderProductsCarousel = () => (
    <div className="mx-2">
      <Carousel
        opts={{ loop: true }}
        className="w-full flex flex-col items-center "
      >
        <CarouselContent className="py-[10px] space-x-4">
          {products &&
            products.map((_, carouselIdx) => {
              const itemsPerSlide = isMobile ? 2 : 4;
              const includePromoContainer =
                !isMobile &&
                carouselIdx === 0 &&
                selectedBrand &&
                selectedBrand.image;

              if (carouselIdx % itemsPerSlide === 0) {
                return (
                  <CarouselItem
                    key={carouselIdx}
                    className={cn(
                      "basis-full grid gap-[10px]",
                      isMobile ? "grid-cols-2" : "regularL:grid-cols-4"
                    )}
                  >
                    {includePromoContainer && (
                      <Link
                        href={
                          selectedBrand
                            ? getBrandPageUrl(selectedBrand.slug)
                            : "/produits/filtres"
                        }
                        className="block"
                      >
                        <Image
                          src={selectedBrand?.image || "/logos/akal-logo.svg"}
                          alt={`${selectedBrand?.name || "AKAL"} brand`}
                          width={330}
                          height={509}
                          className="h-full object-cover rounded-lg hover:opacity-90 transition-opacity cursor-pointer"
                        />
                      </Link>
                    )}

                    {Array.from({
                      length: itemsPerSlide - (includePromoContainer ? 1 : 0),
                    }).map((_, idx) => {
                      const productIndex =
                        carouselIdx + idx + (includePromoContainer ? 0 : 0);
                      return productIndex < products.length ? (
                        <ProductContainer
                          key={products[productIndex].id}
                          product={products[productIndex]}
                        />
                      ) : null;
                    })}
                  </CarouselItem>
                );
              }
              return null;
            })}
        </CarouselContent>
        <CarouselPrevious />
        <CarouselNext />
      </Carousel>
    </div>
  );

  return (
    brandsWithProducts.length > 0 && (
      <div
        className={cn(
          "w-full flex flex-col items-center space-y-8",
          props.className
        )}
      >
        {products && products.length > 0 && (
          <div className="relative flex flex-col items-center ">
            <h3>
              <Text textStyle="TS2" className="font-bold">
                {t("title")}
              </Text>
            </h3>
          </div>
        )}

        {/* Category Filter Buttons for Default and MostSold Variants */}
        {brandsWithProducts && brandsWithProducts.length > 0 && (
          <div className="w-full">
            {/* Mobile horizontal scroll */}
            <div className="md:hidden overflow-x-auto pb-2">
              <div className="flex gap-3 min-w-max px-4">
                {brandsWithProducts.map((brand) => (
                  <Button
                    key={brand.id}
                    onClick={() => setSelectedBrand(brand)}
                    variant="ghost"
                    className={`px-4 py-6 rounded-full text-sm border border-primary transition-colors whitespace-nowrap flex-shrink-0 ${
                      selectedBrand !== null && brand.id === selectedBrand.id
                        ? "bg-primary text-white"
                        : "bg-white text-primary hover:bg-primary hover:text-white"
                    }`}
                  >
                    <Text textStyle="TS6">{brand.name}</Text>
                  </Button>
                ))}
              </div>
            </div>
            <div className="hidden md:flex flex-wrap gap-3 justify-center">
              {brandsWithProducts.map((brand) => (
                <Button
                  key={brand.id}
                  variant="ghost"
                  onClick={() => setSelectedBrand(brand)}
                  className={`px-4 py-6 rounded-full text-sm border border-primary transition-colors whitespace-nowrap flex-shrink-0 ${
                    selectedBrand !== null && brand.id === selectedBrand.id
                      ? "bg-primary text-white"
                      : "bg-white text-primary hover:bg-primary hover:text-white"
                  }`}
                >
                  <Text textStyle="TS6">{brand.name}</Text>
                </Button>
              ))}
            </div>
          </div>
        )}

        {productsAreLoading ? (
          <ProductsOverviewSkeletons />
        ) : (
          products &&
          products.length > 0 && (
            <>
              {shouldUseSwiper
                ? renderProductsCarousel()
                : renderProductsGrid()}

              <Link href={moreProductsLink}>
                <Button variant="voirplus" size="sm">
                  <Text textStyle="TS5">{t("discoverPlus")}</Text>
                </Button>
              </Link>
            </>
          )
        )}
      </div>
    )
  );
}

export function ProductsOverviewSkeletons({
  className,
}: {
  className?: string;
}) {
  return (
    <div
      className={cn("w-full flex flex-col items-center space-y-8", className)}
    >
      <div className="grid regularL:grid-cols-4 grid-cols-2 gap-[10px]">
        {Array.from({ length: 4 }).map((_, idx) => (
          <ProductContainer key={idx} product={null} />
        ))}
      </div>
      <Skeleton className="h-10 w-40 rounded-full" />
    </div>
  );
}

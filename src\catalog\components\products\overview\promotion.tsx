"use client";
import { useTranslations } from "next-intl";
import type { PromotionType } from "@/modules/catalog/types";
import usePromotionProducts from "@/modules/catalog/hooks/promotions/use-products";
import { getPromotionPageUrl } from "@/modules/catalog/utils/urls";
import Link from "next/link";
import { HTMLAttributes } from "react";
import { Button } from "@/components/ui/button";
import Text from "@/styles/text-styles";
import ProductContainer from "../product/container/default";
import Image from "next/image";
import { ProductsOverviewSkeletons } from "./default";
import { cn } from "@/lib/utils";
import { useProductSwiper } from "@/hooks/use-screen-size";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

interface Props extends HTMLAttributes<"div"> {
  promotion: PromotionType;
}

export default function PromotionProductsOverview({
  promotion,
  ...props
}: Props) {
  const t = useTranslations("landingPage.productsOverview");

  const { products, productsAreLoading } = usePromotionProducts({
    limit: 7,
    promotionSlug: promotion ? promotion.slug : "",
  });

  const {
    isMobile,
    shouldUseSwiper,
    showPromotionalContainer,
    isSingleProduct,
  } = useProductSwiper(products?.length || 0);

  const renderProductsGrid = () => (
    <div
      className={cn(
        "grid gap-[10px]",
        isMobile && isSingleProduct
          ? "grid-cols-1 max-w-[280px] mx-auto"
          : "regularL:grid-cols-4 grid-cols-2"
      )}
    >
      {/* Promotional Container - Only on PC */}
      {showPromotionalContainer && (
        <Link href={getPromotionPageUrl(promotion.slug)}>
          <Image
            src="/banners/promotion-card.webp"
            alt="AKAL logo"
            width={330}
            height={509}
            className="h-full object-cover rounded-lg"
          />
        </Link>
      )}

      {/* Products */}
      {products?.map((product) => (
        <ProductContainer key={product.id} product={product} />
      ))}
    </div>
  );

  const renderProductsCarousel = () => (
    <Carousel
      opts={{ loop: true }}
      className="w-full flex flex-col space-y-4 items-center"
    >
      <CarouselContent className="py-[10px]">
        {products &&
          products.map((_, carouselIdx) => {
            const itemsPerSlide = isMobile ? 2 : 4;
            const includePromoContainer = !isMobile && carouselIdx === 0;

            if (carouselIdx % itemsPerSlide === 0) {
              return (
                <CarouselItem
                  key={carouselIdx}
                  className={cn(
                    "basis-full grid gap-[10px]",
                    isMobile ? "grid-cols-2" : "regularL:grid-cols-4"
                  )}
                >
                  {/* Promotional Container - Only on PC and first slide */}
                  {includePromoContainer && (
                    <Link href={getPromotionPageUrl(promotion.slug)}>
                      <Image
                        src="/banners/promotion-card.webp"
                        alt="AKAL logo"
                        width={330}
                        height={509}
                        className="h-full object-cover rounded-lg"
                      />
                    </Link>
                  )}

                  {/* Products */}
                  {Array.from({
                    length: itemsPerSlide - (includePromoContainer ? 1 : 0),
                  }).map((_, idx) => {
                    const productIndex =
                      carouselIdx + idx + (includePromoContainer ? 0 : 0);
                    return productIndex < products.length ? (
                      <ProductContainer
                        key={products[productIndex].id}
                        product={products[productIndex]}
                      />
                    ) : null;
                  })}
                </CarouselItem>
              );
            }
            return null;
          })}
      </CarouselContent>
      <CarouselPrevious />
      <CarouselNext />
    </Carousel>
  );

  return products === undefined || productsAreLoading ? (
    <ProductsOverviewSkeletons />
  ) : (
    products && products.length > 0 && (
      <div
        className={cn(
          "w-full flex flex-col items-center space-y-8",
          props.className
        )}
      >
        <div className="relative flex flex-col items-center py-5">
          <h3>
            <Text textStyle="TS2" className="font-bold">
              {t("specialOffers")}
            </Text>
          </h3>

          <Text textStyle="TS5" className="text-lg text-gray/80 text-center">
            {t("enjoyPromotions")}
          </Text>
        </div>

        {productsAreLoading ? (
          <ProductsOverviewSkeletons />
        ) : (
          products &&
          products.length > 0 && (
            <>
              {shouldUseSwiper
                ? renderProductsCarousel()
                : renderProductsGrid()}

              <Link href={getPromotionPageUrl(promotion.slug)}>
                <Button variant="voirplus" size="sm">
                  <Text textStyle="TS5">{t("discoverPlus")}</Text>
                </Button>
              </Link>
            </>
          )
        )}
      </div>
    )
  );
}

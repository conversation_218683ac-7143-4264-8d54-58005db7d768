import { CriteriaType, ProductsSectionsVariant } from "../types";

export function getCriteriaBasedOnProductsVariant(
  variant: ProductsSectionsVariant
): CriteriaType {
  switch (variant) {
    case "news":
      return "createdAtDesc";
    case "similarProducts":
      return "displayOrder";
    case "recommended":
      return "displayOrder";
    case "mostSold":
      return "mostSold";
  }

  return "displayOrder";
}

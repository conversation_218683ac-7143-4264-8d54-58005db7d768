"use client";
import { useTranslations } from "next-intl";
import { Skeleton } from "@/components/ui/skeleton";
import ProductContainer from "./product/container/default";
import PaginationMangement from "@/components/pagination/pagination-management";
import Text from "@/styles/text-styles";
import usePromotionProducts from "../../hooks/promotions/use-products";
import { notFound } from "next/navigation";

interface Props {
  promotionSlug: string;
}
export default function PromotionProductsLists({ promotionSlug }: Props) {
  const t = useTranslations("filtersPage");

  const {
    products,
    productsAreLoading,
    error,
    setPage,
    page,
    pagesNumber,
    paginatedListRef: scrollViewPortRef,
  } = usePromotionProducts({
    limit: 20,
    promotionSlug,
  });

  if (error && error.status === 404) notFound();

  return (
    <>
      <div ref={scrollViewPortRef} className="L:p-4 w-full">
        {!productsAreLoading ? (
          products && products.length > 0 ? (
            <div className="w-full grid grid-cols-1 S:grid-cols-2 regularL:grid-cols-3 XL:grid-cols-4 gap-2 L:gap-5 transition-all">
              {products.map((product, idx) => (
                <ProductContainer key={idx} product={product} />
              ))}
            </div>
          ) : (
            <div className="h-[450px] min-w-full flex items-center justify-center">
              <p className="text-[50px] text-center">
                <Text textStyle="TS2">{t.raw("NoProducts")}</Text>
              </p>
            </div>
          )
        ) : (
          <div className="w-full grid grid-cols-1 M:grid-cols-2 regularL:grid-cols-3 XL:grid-cols-4 gap-2 L:gap-5 transition-all">
            {Array.from({ length: 12 }).map((_, idx) => (
              <div
                key={idx}
                className="w-full rounded-[20px] flex flex-col justify-between space-y-3 p-2"
              >
                {/* Image Skeleton */}
                <div className="w-full flex-1 flex justify-center items-center">
                  <Skeleton className="h-[300px] w-[300px] rounded-XL" />
                </div>

                {/* Product Details Skeleton */}
                <div className="flex items-center space-x-2">
                  <div className="flex-1 max-w-[80%] flex flex-col space-y-2">
                    {/* Product Name Skeleton */}
                    <Skeleton className="h-4 w-full" />

                    {/* Price Details Skeleton */}
                    <div className="flex space-x-2 items-center">
                      <Skeleton className="h-4 w-[50px]" />
                      <Skeleton className="h-4 w-[40px]" />
                    </div>
                  </div>

                  {/* Cart Icon Skeleton */}
                  <Skeleton className="w-[35px] h-[35px] rounded-full" />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      <div className="flex justify-center mt-4">
        <PaginationMangement
          currentPage={page}
          pagesNumber={pagesNumber}
          changePage={setPage}
        />
      </div>
    </>
  );
}

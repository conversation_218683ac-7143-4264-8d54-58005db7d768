import { Button } from "@/components/ui/button";
import { useCartStore } from "@/modules/cart/store/cart-store";
import { ProductItemType } from "@/modules/cart/types/products";
import Text from "@/styles/text-styles";
import { Minus, Plus } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useRouter } from "next/navigation";
import HeartButton from "@/components/ui/heart-button";
import { useWishlistStore } from "@/modules/wishlist/store/wishlist-store";

interface Props {
  productItem: ProductItemType | null;
}

export default function ProductPurchaseConfigurator({ productItem }: Props) {
  const router = useRouter();
  const [quantity, setQuantity] = useState(1);
  const t = useTranslations("productPage");
  const {
    actions: { addProductItem },
  } = useCartStore();
  const {
    actions: {
      toggleProductItem,
      isProductInWishlist: checkIsProductInWishlist,
    },
  } = useWishlistStore();

  const isProductInWishlist = productItem
    ? checkIsProductInWishlist(productItem.id)
    : false;

  const handleWishlistToggle = () => {
    if (productItem) {
      toggleProductItem({
        slug: productItem.slug,
        productId: productItem.productId,
        id: `temp-${productItem.id}`,
        productItemId: productItem.id,
        name: productItem.name,
        image: productItem.image,
        prices: productItem.prices,
        variations: productItem.variations,
        inStock: productItem.inStock,
      });
    }
  };

  return productItem ? (
    <div className="space-y-4">
      {/* Quantity and Add to Cart */}
      <div className="h-max flex items-stretch gap-2 S:gap-3 L:gap-4">
        <div className="px-2 S:px-3 py-1 S:py-2 flex items-center rounded-full bg-gray/10">
          <Button
            variant="ghost"
            size="icon"
            disabled={quantity <= 1}
            onClick={() => {
              setQuantity(quantity - 1);
            }}
            className="hover:bg-gray-100 h-6 w-6 S:h-8 S:w-8"
          >
            <Minus className="w-2 h-2 S:w-3 S:h-3 text-gray" />
          </Button>

          <Text
            textStyle="TS6"
            className="text-center text-black font-medium w-6 S:w-8 text-sm S:text-base"
          >
            {quantity}
          </Text>

          <Button
            variant="ghost"
            size="icon"
            onClick={() => {
              setQuantity(quantity + 1);
            }}
            className="hover:bg-gray-100 h-6 w-6 S:h-8 S:w-8"
          >
            <Plus className="w-2 h-2 S:w-3 S:h-3 text-gray" />
          </Button>
        </div>

        <Button
          className="rounded-full py-3 S:py-4 L:py-6 flex-1 flex items-center border border-primary bg-primary hover:bg-white hover:text-primary text-sm S:text-base"
          onClick={() => {
            addProductItem({
              slug: productItem.slug,
              id: productItem.id,
              productId: productItem.productId,
              cartQuantity: quantity,
              name: productItem.name,
              prices: productItem.prices,
              image: productItem.image,
              variations: productItem.variations,
              inStock: productItem.inStock,
            });
          }}
        >
          <Text textStyle="TS6">{t("addToCard")}</Text>
        </Button>

        {/* Heart Favorite Button */}
        <HeartButton
          size="md"
          variant="default"
          isLiked={isProductInWishlist}
          onClick={handleWishlistToggle}
        />
      </div>

      {/* Buy Now Button */}
      <Button
        className="rounded-full py-3 S:py-4 L:py-6 flex-1 flex items-center border border-primary bg-primary hover:bg-white hover:text-primary w-full text-sm S:text-base"
        onClick={() => {
          addProductItem(
            {
              slug: productItem.slug,
              id: productItem.id,
              cartQuantity: quantity,
              productId: productItem.productId,
              name: productItem.name,
              prices: productItem.prices,
              image: productItem.image,
              variations: productItem.variations,
              inStock: productItem.inStock,
            },
            false
          );
          router.push("/paiement");
        }}
      >
        <Text textStyle="TS6">{t("buyNow")}</Text>
      </Button>
    </div>
  ) : null;
}

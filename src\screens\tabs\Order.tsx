import React from 'react';
import {View, ScrollView} from 'react-native';

import {text} from '../../text';
import {svg} from '../../assets/svg';
import {useAppSelector} from '../../hooks';
import {components} from '../../components';
import {theme, tabs} from '../../constants';
import {useAppNavigation} from '../../hooks';
import {useGetProductsQuery} from '../../store/slices/apiSlice';

const Order = () => {
  const navigation = useAppNavigation();
  const cart = useAppSelector((state) => state.cartSlice.list);
  const delivery = useAppSelector((state) => state.cartSlice.delivery).toFixed(
    1,
  );
  const subtotal = useAppSelector((state) => state.cartSlice.total).toFixed(1);
  const total = Number(delivery) + Number(subtotal);

  const {data, error, isLoading} = useGetProductsQuery();

  if (isLoading) {
    return <components.Loader />;
  }

  const products = data instanceof Array ? data : [];

  const renderStatusBar = (): JSX.Element => {
    return (
      <components.StatusBar
        backgroundColor={theme.colors.transparent}
        barStyle='dark-content'
      />
    );
  };

  const renderHeader = (): JSX.Element => {
    return (
      <components.Header
        burgerIcon={true}
        basket={true}
        bottomLine={true}
        title={cart.length === 0 ? 'Cart' : ''}
      />
    );
  };

  const renderTabBar = (): JSX.Element => {
    return (
      <components.TabBar>
        {tabs.map((item, index) => {
          return <components.TabBarItem item={item} key={index} />;
        })}
      </components.TabBar>
    );
  };

  const renderProducts = () => {
    return (
      <View style={{marginLeft: 20}}>
        {cart.map((item, index, array) => {
          const lastElement = index === array.length - 1;
          return (
            <components.OrderItem
              key={index}
              item={item}
              lastElement={lastElement}
            />
          );
        })}
      </View>
    );
  };

  const renderEmptyCart = () => {
    return (
      <React.Fragment>
        <svg.ShoppingBagSvg />
        <text.H2 style={{marginTop: 30, marginBottom: 14}}>
          Your cart is empty!
        </text.H2>
        <text.T16>Looks like you have not made {'\n'}your order yet.</text.T16>
      </React.Fragment>
    );
  };

  const renderTotal = () => {
    return (
      <components.Container
        containerStyle={{
          marginHorizontal: 20,
        }}
      >
        <components.ContainerItem
          title='Subtotal'
          price={`$${subtotal}`}
          titleStyle={{
            ...theme.fonts.H5,
            color: theme.colors.mainColor,
          }}
          priceStyle={{
            ...theme.fonts.textStyle_14,
            color: theme.colors.mainColor,
          }}
        />
        <components.ContainerItem
          title='Delivery'
          price={`$${delivery}`}
          containerStyle={{
            marginBottom: 14,
          }}
        />
        <components.ContainerLine />
        <components.ContainerItem
          title='Total'
          price={`$${total}`}
          containerStyle={{
            marginBottom: 0,
          }}
          titleStyle={{
            ...theme.fonts.H4,
            color: theme.colors.mainColor,
          }}
          priceStyle={{
            ...theme.fonts.H4,
            color: theme.colors.mainColor,
          }}
        />
      </components.Container>
    );
  };

  const renderVoucher = () => {
    return (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: 50,
          marginHorizontal: 20,
        }}
      >
        <components.InputField
          placeholder='Promocode'
          label='Enter the voucher'
          containerStyle={{flex: 1, marginRight: 10}}
        />
        <View style={{width: '30%'}}>
          <components.Button
            title='+ add'
            onPress={() => {
              console.log('add');
            }}
          />
        </View>
      </View>
    );
  };

  const renderContent = (): JSX.Element => {
    return (
      <ScrollView
        contentContainerStyle={{
          flexGrow: 1,
          paddingVertical: 20,
          paddingBottom: 20,
          paddingHorizontal: cart.length === 0 ? 20 : 0,
          justifyContent: cart.length === 0 ? 'center' : 'flex-start',
        }}
      >
        {cart.length === 0 ? renderEmptyCart() : renderProducts()}
        {cart.length !== 0 && renderVoucher()}
        {cart.length !== 0 && renderTotal()}
      </ScrollView>
    );
  };

  const renderButton = (): JSX.Element => {
    return (
      <View style={{padding: 20}}>
        <components.Button
          title={cart.length === 0 ? 'shop now' : 'proceed to checkout'}
          onPress={() => {
            if (cart.length === 0) {
              navigation.navigate('Shop', {
                title: 'Shop',
                products: products,
              });
            }
            if (cart.length !== 0) {
              navigation.navigate('Checkout');
            }
          }}
          transparent={true}
        />
      </View>
    );
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderContent()}
      {renderButton()}
      {renderTabBar()}
    </components.SmartView>
  );
};

export default Order;

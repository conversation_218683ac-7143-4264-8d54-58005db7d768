import {
  ItemInResponseType,
  ItemType,
  PriceRange,
  PriceRangeInResponse,
  ProductInResponseType,
  ProductType,
} from '../../types/products';

const BACKEND_ADDRESS = 'https://api-akal.tdg.tn';

export function castToProductTypeRN(
  productInResponse: ProductInResponseType,
): ProductType {
  return {
    metaContent: productInResponse.metaContent
      ? {
          title: productInResponse.metaContent.title || '',
          description: productInResponse.metaContent.description || '',
          keywords: productInResponse.metaContent.keywords || '',
        }
      : null,
    slug: productInResponse.slug,
    brand: productInResponse.brand
      ? {
          slug: productInResponse.brand.slug,
          name: productInResponse.brand.name,
          image: productInResponse.brand.image
            ? `${BACKEND_ADDRESS}${productInResponse.brand.image}`
            : undefined,
        }
      : null,
    details: productInResponse.details,
    categoryIds: productInResponse.categoryIds,
    name: productInResponse.name as string,
    description: productInResponse.description as string,
    id: productInResponse.id,
    items: productInResponse.items.map((item) => castToItemTypeRN(item)),
  };
}

function castToItemTypeRN(item: ItemInResponseType): ItemType {
  return {
    id: item.id,
    reference: item.reference,
    barcode: item.barcode,
    image:
      item.image && item.image !== 'null'
        ? `${BACKEND_ADDRESS}${item.image}`
        : '', // Default placeholder for React Native
    images: item.images
      .map(
        (image) =>
          image && image !== 'null' ? `${BACKEND_ADDRESS}${image}` : '', // Default placeholder for React Native
      )
      .filter((img) => img !== ''), // Remove empty strings
    variations: item.variations || [],
    prices: item.prices.map((price) => {
      return {
        promotionalPrice: Number(price.promotionalPrice),
        realPrice: Number(price.regularPrice),
        currency: price.currency,
      };
    }),
    inStock: item.inStock,
    promotion: item.promotion,
  };
}

export function castToPriceRangeRN(
  priceRangeInResponse: PriceRangeInResponse,
): PriceRange {
  return {
    minPrice: Number(priceRangeInResponse.min),
    maxPrice: Number(priceRangeInResponse.max),
  };
}

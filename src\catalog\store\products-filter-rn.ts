import { create } from "zustand";
import { CriteriaType } from "../types";

// Simplified types for React Native
interface BrandType {
  id: string;
  name: string;
  slug: string;
  image?: string;
}

interface CategoryType {
  id: string;
  name: string;
  slug: string;
  subCategories: CategoryType[];
}

interface CategorySelectionType extends CategoryType {
  selected: boolean;
  subCategories: CategorySelectionType[];
}

interface FilterState {
  joinedPageData: {
    category?: CategoryType | null;
    brand?: BrandType | null;
  };
  joinedPageParam: {
    brandSlug?: string | null;
    categorySlug?: string | null;
    subCategorySlug?: string | null;
    subSubCategorySlug?: string | null;
  };
  priceRangeFilteringVersion: number;
  selectedBrands: BrandType[];
  categories: CategorySelectionType[];
  priceRange: number[];
  criteria: CriteriaType;
  search: string;
  filterVersion: number;
}

interface FilterActions {
  setJoinedPageParam: (params: FilterState["joinedPageParam"]) => void;
  setJoinedPageData: (data: FilterState["joinedPageData"]) => void;
  setSearch: (search: string) => void;
  setCategories: (categories: CategorySelectionType[]) => void;
  initializeCategories: (
    categories: CategoryType[],
    categoriesSlugsInUrl: string[]
  ) => void;
  addBrand: (brand: BrandType) => void;
  removeBrand: (brandId: string) => void;
  setBrands: (brands: BrandType[]) => void;
  setPriceRange: (priceRange: number[]) => void;
  applyPriceRangeFiltering: () => void;
  setCriteria: (criteria: CriteriaType) => void;
  clearAll: () => void;
  resetFilter: () => void;
  clearAllCategories: () => void;
  applyFilter: () => void;
}

const initialState: FilterState = {
  categories: [],
  selectedBrands: [],
  priceRange: [],
  criteria: "displayOrder",
  search: "",
  priceRangeFilteringVersion: 0,
  joinedPageParam: {},
  joinedPageData: {},
  filterVersion: 0,
};

export const useProductsFilteringStoreRN = create<FilterState & FilterActions>(
  (set, get) => ({
    // Initial State
    ...initialState,
    
    // Actions
    setJoinedPageParam: (params) => set({ joinedPageParam: params }),
    setJoinedPageData: (data) => set({ joinedPageData: data }),
    setSearch: (search) => set({ search }),
    setCategories: (categories) => set({ categories }),
    
    initializeCategories: (categories, categoriesSlugsInUrl) =>
      set(() => ({
        categories: categories.map((cat) =>
          castToCategorySelectionType(cat, categoriesSlugsInUrl)
        ),
      })),
      
    addBrand: (brand) =>
      set((state) => ({
        selectedBrands: [...state.selectedBrands, brand],
      })),
      
    removeBrand: (brandId) =>
      set((state) => ({
        selectedBrands: state.selectedBrands.filter(
          (brand) => brand.id !== brandId
        ),
      })),
      
    setBrands: (brands) => set({ selectedBrands: brands }),
    
    setPriceRange: (priceRange) => set({ priceRange }),
    
    applyPriceRangeFiltering: () =>
      set((state) => ({
        priceRangeFilteringVersion: state.priceRangeFilteringVersion + 1,
      })),
      
    setCriteria: (criteria) => set({ criteria }),
    
    clearAllCategories: () =>
      set((state) => ({
        categories: deselectCategories(state.categories),
      })),
      
    clearAll: () =>
      set((state) => ({
        search: "",
        selectedBrands: state.joinedPageData.brand
          ? [state.joinedPageData.brand]
          : [],
        categories: deselectCategories(state.categories),
        filterVersion: state.filterVersion + 1,
      })),
      
    resetFilter: () =>
      set(() => ({
        search: "",
        selectedBrands: [],
        categories: [],
      })),
      
    applyFilter: () =>
      set((state) => ({
        filterVersion: state.filterVersion + 1,
      })),
  })
);

// Helper functions
const castToCategorySelectionType = (
  category: CategoryType,
  categoriesSlugsInUrl?: string[]
): CategorySelectionType => {
  const categorySubCategories = category.subCategories.map((subCategory) =>
    castToCategorySelectionType(subCategory, categoriesSlugsInUrl)
  );

  return {
    ...category,
    subCategories: categorySubCategories,
    selected:
      categoriesSlugsInUrl !== undefined &&
      categoriesSlugsInUrl.includes(category.slug),
  };
};

const deselectCategories = (categories: CategorySelectionType[]) => {
  return categories.map((category) => {
    if (category.selected) {
      category.selected = false;
    }
    if (category.subCategories.length > 0) {
      category.subCategories = deselectCategories(category.subCategories);
    }
    return category;
  });
};

// Export types for use in other files
export type { BrandType, CategoryType, CategorySelectionType };

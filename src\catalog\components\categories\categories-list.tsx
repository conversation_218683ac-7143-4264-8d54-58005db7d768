"use client";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import { HTMLAttributes } from "react";
import { cn } from "@/lib/utils";
import { MoveRight } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import Link from "next/link";
import useCategories from "../../hooks/categories/use-categories";
import { getCategoryPageUrl } from "../../utils/urls";

//a container to display the categories list
export default function CategoriesList(props: HTMLAttributes<"div">) {
  const t = useTranslations("landingPage.categoriesOverview");
  const { categories, categoriesAreLoading } = useCategories();

  return !categoriesAreLoading && categories ? (
    <section
      className={cn(
        "rounded-[20px] bg-primary XL:px-12 L:px-6 px-3 XL:py-7 L:py-4 py-2 flex flex-col space-y-8 text-white",
        props.className
      )}
    >
      <h2>
        <Text textStyle="TS3" className="font-bold">
          {t("attractivenessTitle")}
        </Text>
      </h2>
      <nav aria-label="categories-navigation" className="">
        <ul className="grid grid-cols-2 gap-8">
          {categories.map((category) => (
            <li key={category.id} className="group">
              <Link
                href={getCategoryPageUrl(category)}
                className="group w-full pr-5 flex justify-between items-center"
              >
                <Text
                  textStyle="TS4"
                  className="relative group-hover:before:w-full before:w-0 before:absolute before:content-[''] before:left-0 before:bottom-0 before:h-[1px] before:bg-white before:duration-500"
                >
                  {category.name}
                </Text>
                <MoveRight />
              </Link>
            </li>
          ))}
        </ul>
      </nav>
    </section>
  ) : (
    <div
      className={cn(
        "rounded-[20px] XL:px-12 L:px-6 px-3 XL:py-7 L:py-4 py-2 flex flex-col space-y-8 text-white",
        props.className
      )}
    >
      <Skeleton className="w-[80%] h-10" />
      <nav aria-label="categories-navigation" className="">
        <ul className="grid grid-cols-2 gap-8">
          {Array.from({ length: 7 }).map((_, idx: number) => (
            <li
              key={idx}
              className="pr-5 flex justify-between items-center space-x-2"
            >
              <Skeleton className="w-32 h-8" />
              <Skeleton className="w-10 h-6" />
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
}

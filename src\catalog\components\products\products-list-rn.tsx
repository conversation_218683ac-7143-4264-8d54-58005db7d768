import React, {memo} from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import {ProductType as CatalogProductType} from '../../types/products';
import CatalogProductContainer from './product/container/react-native';
import {theme} from '../../../constants';
import {text} from '../../../text';

type Props = {
  products: CatalogProductType[];
  isLoading: boolean;
  isRefreshing?: boolean;
  onRefresh?: () => void;
  onLoadMore?: () => void;
  hasNextPage?: boolean;
  isLoadingMore?: boolean;
  onAddToCart?: (product: CatalogProductType) => void;
  onToggleWishlist?: (product: CatalogProductType) => void;
  emptyMessage?: string;
  numColumns?: number;
};

const CatalogProductsList: React.FC<Props> = ({
  products,
  isLoading,
  isRefreshing = false,
  onRefresh,
  onLoadMore,
  hasNextPage = false,
  isLoadingMore = false,
  onAddToCart,
  onToggleWishlist,
  emptyMessage = 'No products found',
  numColumns = 2,
}): JSX.Element => {
  const renderProduct = ({item}: {item: CatalogProductType}) => (
    <View style={styles.productWrapper}>
      <CatalogProductContainer
        product={item}
        onAddToCart={onAddToCart}
        onToggleWishlist={onToggleWishlist}
      />
    </View>
  );

  const renderFooter = () => {
    if (!isLoadingMore) return null;

    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size='small' color={theme.colors.mainColor} />
        <text.T14 style={styles.loadingText}>Loading more products...</text.T14>
      </View>
    );
  };

  const renderEmpty = () => {
    if (isLoading) return null;

    return (
      <View style={styles.emptyContainer}>
        <text.T16 style={styles.emptyText}>{emptyMessage}</text.T16>
      </View>
    );
  };

  const handleEndReached = () => {
    if (hasNextPage && !isLoadingMore && onLoadMore) {
      onLoadMore();
    }
  };

  if (isLoading && !isRefreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size='large' color={theme.colors.mainColor} />
        <text.T16 style={styles.loadingText}>Loading products...</text.T16>
      </View>
    );
  }

  return (
    <FlatList
      data={products}
      renderItem={renderProduct}
      keyExtractor={(item) => item.id}
      numColumns={numColumns}
      contentContainerStyle={styles.container}
      showsVerticalScrollIndicator={false}
      refreshControl={
        onRefresh ? (
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.mainColor]}
            tintColor={theme.colors.mainColor}
          />
        ) : undefined
      }
      onEndReached={handleEndReached}
      onEndReachedThreshold={0.1}
      ListFooterComponent={renderFooter}
      ListEmptyComponent={renderEmpty}
      columnWrapperStyle={numColumns > 1 ? styles.row : undefined}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 15,
    paddingBottom: 100, // Extra padding for bottom navigation
  },
  productWrapper: {
    flex: 1,
    marginHorizontal: 7.5,
  },
  row: {
    justifyContent: 'space-between',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  loadingText: {
    color: theme.colors.textColor,
    marginTop: 10,
    textAlign: 'center',
  },
  footerLoader: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  emptyText: {
    color: theme.colors.textColor,
    textAlign: 'center',
    opacity: 0.7,
  },
});

export default memo(CatalogProductsList);

"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Text from "@/styles/text-styles";
import ProductContainer from "../product/container/default";
import { type HTMLAttributes, useState, useEffect } from "react";
import type { ProductsSectionsVariant } from "../../../types";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { Skeleton } from "@/components/ui/skeleton";
import {
  getCategoryPageUrl,
  getBrandPageUrl,
  getSimilarProductsPage,
} from "@/modules/catalog/utils/urls";
import type { CategoryType } from "@/modules/catalog/types/categories";
import { getCriteriaBasedOnProductsVariant } from "@/modules/catalog/utils/criteria-based-on-variant";
import { useTranslations } from "next-intl";
import useProducts from "@/modules/catalog/hooks/products/use-products";
import { BrandType } from "@/modules/catalog/types/brands";
import Image from "next/image";
import useCategories from "@/modules/catalog/hooks/categories/use-categories";
import useScreenSize, { useProductSwiper } from "@/hooks/use-screen-size";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

interface Props extends HTMLAttributes<"html"> {
  maxProductsNumber?: number;
  variant?: ProductsSectionsVariant;
  similarProductSlug?: string;
  similarProductName?: string;
  category?: CategoryType;
  brand?: BrandType;
}

export default function ProductsOverview({
  variant = "default",
  ...props
}: Props) {
  const t = useTranslations("landingPage.productsOverview");
  const sharedContent = useTranslations("shared.sections");
  const { width: screenWidth } = useScreenSize();

  // Category filtering
  const [selectedCategory, setSelectedCategory] = useState<CategoryType | null>(
    null
  );
  const { categories } = useCategories();
  const categoriesThatHasProducts =
    categories &&
    categories.filter(
      (cat) => cat.numberOfProducts && cat.numberOfProducts > 0
    );

  useEffect(() => {
    if (
      categoriesThatHasProducts &&
      categoriesThatHasProducts.length > 0 &&
      !selectedCategory
    ) {
      setSelectedCategory(categoriesThatHasProducts[0]);
    }
  }, [variant, categories, selectedCategory]);

  const { products, productsAreLoading } = useProducts({
    limit: screenWidth < 740 ? 5 : 7,
    queryKeys: [screenWidth],
    criteria: getCriteriaBasedOnProductsVariant(
      variant === "selection" ? "default" : variant
    ),
    similarProductSlug: props.similarProductSlug,
    categoriesSlugs:
      (variant === "default" || variant === "mostSold") && selectedCategory
        ? selectedCategory.subCategories.length === 0
          ? [selectedCategory.slug]
          : [
              selectedCategory.slug,
              ...selectedCategory.subCategories
                .flatMap((cat) =>
                  cat.subCategories.length > 0 ? cat.subCategories : [cat]
                )
                .map((cat) => cat.slug),
            ]
        : undefined,
  });

  const {
    isMobile,
    shouldUseSwiper,
    showPromotionalContainer,
    isSingleProduct,
  } = useProductSwiper(products?.length || 0);

  const moreProductsLink =
    variant === "productsByCategory" && props.category
      ? getCategoryPageUrl(props.category)
      : props.similarProductSlug
      ? getSimilarProductsPage(props.similarProductSlug)
      : variant === "news"
      ? "/produits/filtres?sort=newer"
      : variant === "mostSold"
      ? "/produits/filtres?criteria=mostSold"
      : "/produits/filtres";

  const getSectionImage = () => {
    if (props.category?.bannerImage) {
      return props.category.bannerImage;
    }

    if (props.brand?.image) {
      return props.brand.image;
    }

    if (selectedCategory?.bannerImage) {
      return selectedCategory.bannerImage;
    }
  };

  const renderProductsGrid = () => (
    <div
      className={cn(
        "grid gap-[10px]",
        isMobile && isSingleProduct
          ? "grid-cols-1 w-[250px] M:w-[340px] mx-auto"
          : "regularL:grid-cols-4 grid-cols-2"
      )}
    >
      {showPromotionalContainer && variant !== "similarProducts" && (
        <Link
          href={
            props.category
              ? getCategoryPageUrl(props.category)
              : props.brand
              ? getBrandPageUrl(props.brand.slug)
              : selectedCategory
              ? getCategoryPageUrl(selectedCategory)
              : "/produits/filtres"
          }
          className="block"
        >
          <Image
            src={getSectionImage() || "/not-found/image.png"}
            alt={
              selectedCategory
                ? selectedCategory.name
                : `AKAL ${variant} promotion`
            }
            width={330}
            height={509}
            className="h-full object-cover rounded-lg hover:opacity-90 transition-opacity cursor-pointer"
          />
        </Link>
      )}

      {/* Products */}
      {products?.map((product) => (
        <ProductContainer key={product.id} product={product} />
      ))}
    </div>
  );

  const renderProductsCarousel = () => (
    <Carousel
      opts={{ loop: true }}
      className="w-full flex flex-col space-y-4 items-center "
    >
      <CarouselContent className="py-[10px]">
        {products &&
          products.map((_, carouselIdx) => {
            const itemsPerSlide = isMobile ? 2 : 4;
            const includePromoContainer =
              !isMobile && carouselIdx === 0 && variant !== "similarProducts";

            if (carouselIdx % itemsPerSlide === 0) {
              return (
                <CarouselItem
                  key={carouselIdx}
                  className={cn(
                    "basis-full grid gap-[10px]",
                    isMobile ? "grid-cols-2" : "regularL:grid-cols-4"
                  )}
                >
                  {/* Promotional Container - Only on PC and first slide */}
                  {includePromoContainer && (
                    <Link
                      href={
                        props.category
                          ? getCategoryPageUrl(props.category)
                          : props.brand
                          ? getBrandPageUrl(props.brand.slug)
                          : selectedCategory
                          ? getCategoryPageUrl(selectedCategory)
                          : "/produits/filtres"
                      }
                      className="block"
                    >
                      <Image
                        src={getSectionImage() || "/not-found/image.png"}
                        alt={
                          selectedCategory
                            ? selectedCategory.name
                            : `AKAL ${variant} promotion`
                        }
                        width={330}
                        height={509}
                        className="h-full object-cover rounded-lg hover:opacity-90 transition-opacity cursor-pointer"
                      />
                    </Link>
                  )}

                  {/* Products */}
                  {Array.from({
                    length: itemsPerSlide - (includePromoContainer ? 1 : 0),
                  }).map((_, idx) => {
                    const productIndex =
                      carouselIdx + idx + (includePromoContainer ? 0 : 0);
                    return productIndex < products.length ? (
                      <ProductContainer
                        key={products[productIndex].id}
                        product={products[productIndex]}
                      />
                    ) : null;
                  })}
                </CarouselItem>
              );
            }
            return null;
          })}
      </CarouselContent>
      <CarouselPrevious />
      <CarouselNext />
    </Carousel>
  );

  return (
    <div
      className={cn(
        "w-full flex flex-col items-center space-y-8",
        props.className
      )}
    >
      {products && products.length > 0 && (
        <div className="relative flex flex-col items-center ">
          <h3>
            <Text textStyle="TS2" className="font-bold">
              {variant === "news"
                ? t("discoverNews")
                : ["similarProducts"].includes(variant)
                ? sharedContent("youMayLike")
                : variant === "productsByCategory"
                ? props.category?.name
                : variant === "mostSold"
                ? t("mostSold")
                : t("nosRecommendations")}
            </Text>
          </h3>

          {variant !== "default" && (
            <Text textStyle="TS5" className="text-lg text-gray/80 text-center">
              {variant === "mostSold"
                ? t("mostSoldDescription")
                : t("selectionDescription")}
            </Text>
          )}
        </div>
      )}

      {/* Category Filter Buttons for Default and MostSold Variants */}
      {(variant === "default" || variant === "mostSold") &&
        categoriesThatHasProducts &&
        categoriesThatHasProducts.length > 0 && (
          <div className="w-full">
            {/* Mobile horizontal scroll */}
            <div className="md:hidden overflow-x-auto pb-2">
              <div className="flex gap-3 min-w-max px-4">
                {categoriesThatHasProducts.map((cat) => (
                  <Button
                    key={cat.id}
                    onClick={() => setSelectedCategory(cat)}
                    variant="ghost"
                    className={`px-4 py-6 rounded-full text-sm border border-primary transition-colors whitespace-nowrap flex-shrink-0 ${
                      selectedCategory !== null &&
                      cat.id === selectedCategory.id
                        ? "bg-primary text-white"
                        : "bg-white text-primary hover:bg-primary hover:text-white"
                    }`}
                  >
                    <Text textStyle="TS6">{cat.name}</Text>
                  </Button>
                ))}
              </div>
            </div>
            <div className="hidden md:flex flex-wrap gap-3 justify-center">
              {categoriesThatHasProducts.map((cat) => (
                <Button
                  key={cat.id}
                  variant="ghost"
                  onClick={() => setSelectedCategory(cat)}
                  className={`px-4 py-6 rounded-full text-sm border border-primary transition-colors whitespace-nowrap flex-shrink-0 ${
                    selectedCategory !== null && cat.id === selectedCategory.id
                      ? "bg-primary text-white"
                      : "bg-white text-primary hover:bg-primary hover:text-white"
                  }`}
                >
                  <Text textStyle="TS6">{cat.name}</Text>
                </Button>
              ))}
            </div>
          </div>
        )}

      {productsAreLoading ? (
        <ProductsOverviewSkeletons />
      ) : (
        products &&
        products.length > 0 && (
          <>
            {shouldUseSwiper ? renderProductsCarousel() : renderProductsGrid()}

            <Link href={moreProductsLink}>
              <Button variant="voirplus" size="sm">
                <Text textStyle="TS5">{t("discoverPlus")}</Text>
              </Button>
            </Link>
          </>
        )
      )}
    </div>
  );
}

export function ProductsOverviewSkeletons({
  className,
}: {
  className?: string;
}) {
  return (
    <div
      className={cn("w-full flex flex-col items-center space-y-8", className)}
    >
      <div className="w-full grid regularL:grid-cols-4 grid-cols-2 gap-[10px]">
        {Array.from({ length: 4 }).map((_, idx) => (
          <ProductContainer key={idx} product={null} />
        ))}
      </div>
      <Skeleton className="h-10 w-40 rounded-full" />
    </div>
  );
}

import { GET } from "@/lib/http-methods";
import { VariationInResponseType } from "../../types/variations";
import { castToGroupedVariationType } from "../../utils/types-casting/variations";

interface Params {
  productSlug?: string;
  categoriesSlugs?: string[];
  brandSlugs?: string[];
  search?: string;
}

export async function retrieveVariationsFromServerSide({
  productSlug,
  categoriesSlugs,
  brandSlugs,
  search,
}: Params = {}) {
  try {
    const params = [];

    // Conditionally add the query parameters to the params array
    if (productSlug) {
      params.push(`productSlug=${productSlug}`);
    }

    if (categoriesSlugs && categoriesSlugs.length > 0) {
      params.push(`categorySlugs=${categoriesSlugs.join(",")}`);
    }

    if (brandSlugs && brandSlugs.length > 0) {
      params.push(`brandSlugs=${brandSlugs.join(",")}`);
    }

    if (search) {
      params.push(`search=${search}`);
    }

    const queryString = params.length > 0 ? `?${params.join("&")}` : "";
    const endpoint = `/products/variations${queryString}`;

    const res = await GET(endpoint, {});

    return (res.data as VariationInResponseType[]).map((variationInResponse) =>
      castToGroupedVariationType(variationInResponse)
    );
  } catch (error) {
    return null;
  }
}

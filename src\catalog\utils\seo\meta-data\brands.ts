import { metadata } from "@/app/layout";
import { retrieveBrandFromServerSide } from "@/modules/catalog/services/brands/brand-extraction";
import { AxiosError } from "axios";
import { Metadata } from "next";
import { notFound } from "next/navigation";

interface Params {
  brandSlug: string;
}

export async function generateBrandMetaData({
  brandSlug,
}: Params): Promise<Metadata> {
  try {
    const brand = await retrieveBrandFromServerSide(brandSlug);

    if (brand?.metaContent) {
      return {
        title: brand.metaContent.title,
        description: brand.metaContent.description,
        keywords: brand.metaContent.keywords,
      };
    }

    return metadata;
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError?.response?.status === 404) notFound();

    return metadata;
  }

  return metadata;
}

import { CategoryType } from "../types/categories";

export function getProductPageUrl(slug: string): string {
  return `/produits/${slug}`;
}

export function getSimilarProductsPage(slug: string): string {
  return `/produits/similaires/${slug}`;
}

export function getBrandPageUrl(slug: string): string {
  return `/marques/${slug}`;
}

export function getCategoryPageUrl(category: CategoryType): string {
  return `/${category.slug}`;
}

export function getSubCategoryPageUrl(
  category: CategoryType,
  subCategory: CategoryType
): string {
  return `/${category.slug}/${subCategory.slug}`;
}

export function getSubSubCategoryPageUrl(
  category: CategoryType,
  subCategory: CategoryType,
  subSubCategory: CategoryType
): string {
  return `/${category.slug}/${subCategory.slug}/${subSubCategory.slug}`;
}

export function getPromotionPageUrl(promotionSlug: string) {
  return `/promotions/${promotionSlug}`;
}

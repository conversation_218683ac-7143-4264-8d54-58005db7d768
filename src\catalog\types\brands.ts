import { SeoMetaContentType, SeoMetaContentTypeInResponse } from "@/types";

export interface BrandType {
  id: string;
  slug: string;
  name: string;
  description: string | null;
  image: string;
  metaContent: SeoMetaContentType | null;
  numberOfProducts: number;
}

export interface BrandInResponseType {
  id: string;
  slug: string;
  name: string;
  description: string | null;
  image: string;
  metaContent: SeoMetaContentTypeInResponse | null;
  numberOfProducts: number;
}

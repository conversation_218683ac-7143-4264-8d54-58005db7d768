import {GET} from '../../lib/http-methods';
import {PaginationType} from '../../types/pagination';
import {ProductInResponseType} from '../../types/products';
import {castToProductTypeRN} from '../../utils/types-casting/products-rn';

interface Params {
  page: number;
  limit: number;
  productSlug: string;
}

export async function retrieveSimilarProductsFromServerSide({
  page,
  limit,
  productSlug,
}: Params) {
  try {
    const res = await GET(
      `/products/similar/${productSlug}?page=${page}&limit=${limit}`,
      {},
    );

    return {
      pagination: res.data.pagination as PaginationType,
      products: (res.data.data as ProductInResponseType[]).map(
        (productInResponse) => castToProductTypeRN(productInResponse),
      ),
    };
  } catch (error) {
    console.error('Error fetching similar products:', error);
    return null;
  }
}

"use client";
import { Button } from "@/components/ui/button";
import Text from "@/styles/text-styles";
import Link from "next/link";
import { cn } from "@/lib/utils";

interface Props {
  title: string;
  subtitle?: string;
  backgroundImage: string;
  buttonText: string;
  buttonUrl: string;
  className?: string;
  titleClassName?: string;
  subtitleClassName?: string;
}

export default function PromotionalContainer({
  title,
  subtitle,
  backgroundImage,
  buttonText,
  buttonUrl,
  className,
  titleClassName,
  subtitleClassName,
}: Props) {
  return (
    <div
      className={cn(
        "relative w-full h-full rounded-lg overflow-hidden bg-cover bg-center bg-no-repeat flex flex-col justify-between p-6",
        className
      )}
      style={{
        backgroundImage: `url(${backgroundImage})`,
      }}
    >
      {/* Overlay for better text readability */}
      <div className="absolute inset-0 bg-black/20" />

      {/* Content */}
      <div className="relative z-10 flex flex-col justify-between h-full">
        {/* Title and Subtitle */}
        <div className="space-y-2">
          <h3 className={cn("text-white font-bold", titleClassName)}>
            <Text textStyle="TS2">{title}</Text>
          </h3>
          {subtitle && (
            <p className={cn("text-white", subtitleClassName)}>
              <Text textStyle="TS6">{subtitle}</Text>
            </p>
          )}
        </div>

        {/* Button */}
        <div className="mt-auto">
          <Link href={buttonUrl}>
            <Button
              size="sm"
              className="bg-white text-black hover:bg-primary hover:text-white rounded-full"
            >
              <Text textStyle="TS6">{buttonText}</Text>
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}

import { GET } from "@/lib/http-methods";
import { castToProductType } from "../../utils/types-casting/products";
import { CustomError } from "@/utils/custom-error";
import { AxiosError } from "axios";

export async function retrieveProductFromServerSide(slug: string) {
  try {
    const res = await GET(`/products/${slug}`, {});

    return castToProductType(res.data);
  } catch (error) {
    const errorResponse = (error as AxiosError).response as {
      data: { message: string };
      status: number;
    };

    throw new CustomError(
      errorResponse.data?.message as string,
      errorResponse.status as number
    );
  }
}

import { GET } from "@/lib/http-methods";
import { BrandInResponseType } from "../../types/brands";
import { castToBrandType } from "../../utils/types-casting/brands";

export async function retrieveBrandFromServerSide(brandSlug: string) {
  try {
    const res = await GET(`/brands/${brandSlug}`, {});

    return castToBrandType(res.data as BrandInResponseType);
  } catch (error) {
    return null;
  }
}

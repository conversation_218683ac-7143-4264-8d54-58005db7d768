import useBrands from "@/modules/catalog/hooks/brands/use-brands";
import {
  getSelectedCategories,
  useProductsFilteringStore,
} from "@/modules/catalog/store/products-filter";
import { BrandType } from "@/modules/catalog/types/brands";
import FilterChoiceContainer from "./filter-choice-container";
import { useEffect, useState } from "react";
import Text from "@/styles/text-styles";

export default function BrandsSelectionDropDown() {
  const { priceRange, search } = useProductsFilteringStore((store) => store);
  const { brands, brandsAreLoading } = useBrands({
    limit: 500,
    categoriesSlugs: getSelectedCategories().map((cat) => cat.slug),
    productPriceRange: priceRange,
    searchByProductName: search,
  });

  const brandsWithProducts =
    brands?.filter((brand) => brand.numberOfProducts > 0) || [];

  return (
    brandsWithProducts?.length > 0 && (
      <FilterChoiceContainer
        title="Marques"
        count={(brandsWithProducts || []).length}
      >
        <div className="space-y-3">
          {brandsWithProducts.map((brand) => (
            <BrandSelection key={brand.id} brand={brand} />
          ))}
        </div>
      </FilterChoiceContainer>
    )
  );
}

function BrandSelection({ brand }: { brand: BrandType }) {
  const [checked, setChecked] = useState<boolean>(false);
  const { addBrand, removeBrand, applyFilter, selectedBrands } =
    useProductsFilteringStore();

  const handleBrandClick = () => {
    if (checked) {
      removeBrand(brand.id);
    } else {
      addBrand(brand);
    }
    applyFilter();
    setChecked(!checked);
  };

  useEffect(() => {
    const isSelected = selectedBrands.find(
      (selectedBrand) => selectedBrand.id === brand.id
    );
    setChecked(!!isSelected);
  }, [selectedBrands, brand.id]);

  return (
    <div
      className="flex items-center space-x-3 w-fit cursor-pointer hover:bg-gray-50 rounded-md py-1 transition-colors px-2"
      onClick={handleBrandClick}
    >
      {/* Custom Circular Checkbox */}
      <div
        className={`w-4 h-4 rounded-md flex items-center justify-center p-0.5 bg-gray-light `}
      >
        <div
          className={`w-full h-full rounded-sm flex items-center justify-center  ${
            checked && "bg-blue "
          }`}
        ></div>
      </div>

      <Text
        textStyle="TS6"
        className={`text-gray-dark font-bold group-hover:font-bold ${
          checked && "text-blue"
        }`}
      >
        {brand.name} ({brand.numberOfProducts})
      </Text>
    </div>
  );
}

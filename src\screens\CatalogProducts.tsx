import React from 'react';
import {View, StyleSheet, SafeAreaView, StatusBar, Alert} from 'react-native';
import {QueryClientProvider} from '@tanstack/react-query';
import {useAppDispatch} from '../store';
import {addToCart} from '../store/slices/cartSlice';
import {addToWishlist} from '../store/slices/wishlistSlice';

// Catalog imports
import {catalogQueryClient} from '../catalog/lib/query-client';
import {useProductsFilteringStoreRN} from '../catalog/store/products-filter-rn';
import useProductsRN from '../catalog/hooks/products/use-products-rn';
import CatalogProductsList from '../catalog/components/products/products-list-rn';
import {ProductType as CatalogProductType} from '../catalog/types/products';
import {adaptCatalogProductToApp} from '../catalog/utils/type-adapters';

// App imports
import {theme} from '../constants';
import {Header} from '../components';

const PRODUCTS_PER_PAGE = 10;

const CatalogProductsScreen: React.FC = (): JSX.Element => {
  const dispatch = useAppDispatch();
  const [localSearch, setLocalSearch] = useState('');

  // Catalog store state
  const {
    selectedBrands,
    categories,
    priceRange,
    criteria,
    search,
    filterVersion,
    setSearch,
  } = useProductsFilteringStoreRN();

  // Get selected category slugs
  const getSelectedCategorySlugs = () => {
    const selectedCategories: string[] = [];
    const extractSlugs = (cats: any[]) => {
      cats.forEach((cat) => {
        if (cat.selected) {
          selectedCategories.push(cat.slug);
        }
        if (cat.subCategories?.length > 0) {
          extractSlugs(cat.subCategories);
        }
      });
    };
    extractSlugs(categories);
    return selectedCategories;
  };

  // Use catalog products hook
  const {
    products,
    productsAreLoading,
    productsError,
    setPage,
    page,
    pagesNumber,
    refetch,
  } = useProductsRN({
    limit: PRODUCTS_PER_PAGE,
    criteria,
    categoriesSlugs: getSelectedCategorySlugs(),
    brandSlugs: selectedBrands.map((brand) => brand.slug),
    search,
    queryKeys: [filterVersion], // Re-fetch when filters change
  });

  // Handle add to cart
  const handleAddToCart = (catalogProduct: CatalogProductType) => {
    try {
      const appProduct = adaptCatalogProductToApp(catalogProduct);
      dispatch(addToCart(appProduct));
      Alert.alert('Success', 'Product added to cart!');
    } catch (error) {
      Alert.alert('Error', 'Failed to add product to cart');
    }
  };

  // Handle wishlist toggle
  const handleToggleWishlist = (catalogProduct: CatalogProductType) => {
    try {
      const appProduct = adaptCatalogProductToApp(catalogProduct);
      // For simplicity, always add to wishlist
      // You can implement wishlist state checking here
      dispatch(addToWishlist(appProduct));
      Alert.alert('Success', 'Product added to wishlist!');
    } catch (error) {
      Alert.alert('Error', 'Failed to update wishlist');
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    refetch();
  };

  // Handle load more (pagination)
  const handleLoadMore = () => {
    if (page < pagesNumber) {
      setPage(page + 1);
    }
  };

  // Handle search
  const handleSearchSubmit = () => {
    setSearch(localSearch);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle='dark-content'
        backgroundColor={theme.colors.white}
        translucent={false}
      />

      {/* Header */}
      <Header title='Products' goBack={true} basket={true} search={true} />

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder='Search products...'
          value={localSearch}
          onChangeText={setLocalSearch}
          onSubmitEditing={handleSearchSubmit}
          returnKeyType='search'
        />
        <TouchableOpacity
          style={styles.searchButton}
          onPress={handleSearchSubmit}
        >
          <Text style={styles.searchButtonText}>Search</Text>
        </TouchableOpacity>
      </View>

      {/* Products List */}
      <View style={styles.content}>
        <CatalogProductsList
          products={products || []}
          isLoading={productsAreLoading}
          onRefresh={handleRefresh}
          onLoadMore={handleLoadMore}
          hasNextPage={page < pagesNumber}
          onAddToCart={handleAddToCart}
          onToggleWishlist={handleToggleWishlist}
          emptyMessage='No products found. Try adjusting your filters.'
        />
      </View>
    </SafeAreaView>
  );
};

// Wrapper component with QueryClient provider
const CatalogProductsScreenWithProvider: React.FC = () => {
  return (
    <QueryClientProvider client={catalogQueryClient}>
      <CatalogProductsScreen />
    </QueryClientProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.white,
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: theme.colors.white,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.lightGray,
  },
  searchInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderColor: theme.colors.lightGray,
    borderRadius: 8,
    paddingHorizontal: 12,
    marginRight: 10,
    fontSize: 16,
  },
  searchButton: {
    backgroundColor: theme.colors.mainColor,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    justifyContent: 'center',
  },
  searchButtonText: {
    color: theme.colors.white,
    fontWeight: '600',
    fontSize: 16,
  },
  content: {
    flex: 1,
  },
});

export default CatalogProductsScreenWithProvider;

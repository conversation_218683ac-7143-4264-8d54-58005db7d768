import { MetadataRoute } from "next";
import { retrieveBrandsFromServerSide } from "../../../services/brands/brands-extraction";

export async function getBrandsPages(): Promise<MetadataRoute.Sitemap> {
  try {
    const brands = await retrieveBrandsFromServerSide({});

    if (brands) {
      const brandsPages: MetadataRoute.Sitemap = brands.brands.map((brand) => ({
        url: `https://${process.env.FRONTEND_DOMAIN_NAME}/marques/${brand.slug}`,
        lastModified: new Date(),
        changeFrequency: "monthly",
        priority: 1,
      }));

      return [
        //brands page
        {
          url: `https://${process.env.FRONTEND_DOMAIN_NAME}/marques`,
          changeFrequency: "yearly",
          priority: 0.5,
        },
        ...brandsPages,
      ];
    }
  } catch {
    return [];
  }

  return [];
}

import { GET } from "@/lib/http-methods";
import { PromotionInResponse } from "../../types";
import { castToPromtionType } from "../../utils/types-casting/promotions";

interface Params {
  limit?: number;
}

export default async function retrievePromotionsFromServerSide({
  limit,
}: Params) {
  try {
    const params = [];
    if (limit) params.push(`limit=${limit}`);
    const query = params.join("&");

    const endpoint = "/promotions";
    const res = await GET(`${endpoint}?${query}`, {});

    return (res.data as PromotionInResponse[]).map((promotionInResponse) =>
      castToPromtionType(promotionInResponse)
    );
  } catch (error) {
    return [];
  }
}

import React from 'react';
import {ScrollView, TouchableOpacity, Text} from 'react-native';

import {useAppSelector} from '../../hooks';

import _v1 from './versions/_v1';
import _v2 from './versions/_v2';

import {theme} from '../../constants';
import {useAppDispatch} from '../../hooks';
import {components} from '../../components';
import {statusBarHeight} from '../../utils';
import {setOrderHistoryVersion} from '../../store/slices/appStateSlice';

const OrderHistory: React.FC = (): JSX.Element => {
  const dispatch = useAppDispatch();
  const version = useAppSelector((state) => state.appState.orderHistoryVersion);

  const switchVersion = () => {
    return (
      <TouchableOpacity
        style={{
          position: 'absolute',
          backgroundColor: theme.colors.mainColor,
          padding: 10,
          zIndex: 1,
          borderTopRightRadius: 5,
          borderBottomRightRadius: 5,
          marginTop: statusBarHeight() + 70,
        }}
        onPress={() => {
          dispatch(setOrderHistoryVersion(version === 1 ? 2 : 1));
        }}
      >
        <Text
          style={{
            color: theme.colors.white,
            ...theme.fonts.DMSans_400Regular,
          }}
        >
          swith version
        </Text>
      </TouchableOpacity>
    );
  };

  const renderStatusBar = (): JSX.Element => {
    return <components.StatusBar />;
  };

  const renderHomeIndicator = (): JSX.Element => {
    return <components.HomeIndicator />;
  };

  const renderHeader: () => JSX.Element = () => {
    return <components.Header goBack={true} title='Order history' />;
  };

  const renderContent: () => JSX.Element = () => {
    return (
      <ScrollView
        contentContainerStyle={{
          paddingTop: 15,
          flexGrow: 1,
        }}
      >
        {version === 1 && <_v1 />}
        {version === 2 && <_v2 />}
      </ScrollView>
    );
  };

  return (
    <components.SmartView>
      {switchVersion()}
      {renderStatusBar()}
      {renderHeader()}
      {renderContent()}
      {renderHomeIndicator()}
    </components.SmartView>
  );
};

export default OrderHistory;
